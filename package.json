{"name": "investor-home", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint", "lint:fix": "eslint --fix"}, "dependencies": {"@tanstack/react-query": "^5.85.9", "@tanstack/react-table": "^8.21.3", "@untitledui-pro/icons": "^0.0.2", "@untitledui/file-icons": "^0.0.9", "@untitledui/icons": "^0.0.19", "dayjs": "^1.11.18", "motion": "^12.23.12", "next": "15.5.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-aria-components": "^1.12.1", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-react-aria-components": "^2.0.1", "usehooks-ts": "^3.1.1", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "eslint": "^9.34.0", "eslint-config-next": "^15.5.2", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "tailwindcss": "^4", "typescript": "^5"}}