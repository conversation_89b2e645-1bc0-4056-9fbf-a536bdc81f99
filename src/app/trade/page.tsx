"use client";
import { Tabs } from "@/components/application/tabs/tabs";
import { Input } from "@/components/base/input/input";
import Header from "@/features/Header";
import { Footer } from "@/features/index/Footer";
import TradingCard from "@/features/trade/TradingCard";
import {
  useReadSearchParams,
  useUpdateSearchParams,
} from "@/hooks/useUpdateSearchParams";
import { StatusFilter, tradeStatus, TradingCardData } from "@/types/interface";
import { TabType, ViewMode } from "@/types/trade";
import { Grid01, List, SearchLg } from "@untitledui-pro/icons/solid";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

const Trade = () => {
  const searchParams = useSearchParams();
  const tabFromUrl = (searchParams.get("type") as TabType) || "current";
  const updateSearchParams = useUpdateSearchParams(tabFromUrl);
  const readSearchParams = useReadSearchParams(tabFromUrl);
  const urlParams = readSearchParams();
  const [activeTab, setActiveTab] = useState<TabType>(tabFromUrl);
  const [viewMode, setViewMode] = useState<ViewMode>(
    urlParams.viewMode || "card",
  );
  const [statusFilter, setStatusFilter] = useState<StatusFilter>(
    urlParams.statusFilter || "all",
  );
  const [searchQuery, setSearchQuery] = useState(urlParams.search || "");

  // 分页状态
  const [currentData, setCurrentData] = useState<TradingCardData[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);

  const tradeTypeTabs = [
    { id: "current", label: "募集中" },
    { id: "past", label: "往期" },
  ];

  const tradeTabs = [
    { id: "all", label: "全部" },
    { id: "ongoing", label: "运作中" },
    { id: "over", label: "已完结" },
  ];
  const viewTabs = [
    { id: "card", label: <Grid01 /> },
    { id: "list", label: <List /> },
  ];

  const generateMockData = (
    count: number,
    startId: number = 1,
  ): TradingCardData[] => {
    const statuses: Array<tradeStatus> = ["current", "ongoing", "over"];
    const issuers = [
      "发行方A",
      "A股上市跨境电商",
      "华融集团",
      "招商银行",
      "平安信托",
      "中信证券",
    ];
    const assetTypes = [
      "AAA资产",
      "BB+债券",
      "贸易融资",
      "供应链金融",
      "房地产基金",
    ];

    return Array.from({ length: count }, (_, i) => {
      const id = startId + i;
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      return {
        id: id.toString(),
        issuer: issuers[Math.floor(Math.random() * issuers.length)],
        assetType: assetTypes[Math.floor(Math.random() * assetTypes.length)],
        scale: "中等规模",
        duration: "12个月",
        lockPeriod: `${3 + Math.floor(Math.random() * 15)}个月`,
        status,
        rate: `${(5 + Math.random() * 5).toFixed(1)}%`,
        targetAmount:
          status === "current" ? 10000 + Math.floor(Math.random() * 90000) : 0,
        unitPrice:
          status === "current" ? 10 + Math.floor(Math.random() * 90) : 0,
        remaining:
          status === "current" ? 1000 + Math.floor(Math.random() * 9000) : 0,
        remainingShares:
          status === "current" ? 100 + Math.floor(Math.random() * 400) : 0,
        assetValue:
          status === "ongoing" || status === "over"
            ? 10000 + Math.floor(Math.random() * 90000)
            : 0,
        progress: status === "current" ? Math.floor(Math.random() * 100) : 0,
        deadline: "2025年8月10日",
        tags: [
          { label: "AAA", color: "brand" as const },
          { label: "低风险", color: "success" as const },
        ],
        supportedCurrencies: ["USD", "CNY", "EUSD"],
      };
    });
  };

  const getFilteredData = useCallback(
    (data: TradingCardData[]) => {
      let filtered = data;

      // Tab筛选
      if (activeTab === "current") {
        filtered = filtered.filter((item) => item.status === "current");
      } else {
        filtered = filtered.filter(
          (item) => item.status === "ongoing" || item.status === "over",
        );
      }

      // 状态筛选
      if (statusFilter !== "all") {
        filtered = filtered.filter((item) => item.status === statusFilter);
      }

      // 搜索筛选
      if (searchQuery) {
        filtered = filtered.filter(
          (item) =>
            item.issuer.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.assetType.toLowerCase().includes(searchQuery.toLowerCase()),
        );
      }

      return filtered;
    },
    [activeTab, statusFilter, searchQuery],
  );

  const loadData = useCallback(
    async (pageNum: number, reset: boolean = false) => {
      setLoading(true);

      await new Promise((resolve) => setTimeout(resolve, 800));

      const pageSize = pageNum === 1 ? 20 : 10;
      const startId = reset ? 1 : (pageNum - 1) * 10 + 1;
      const newData = generateMockData(pageSize, startId);

      if (reset) {
        setCurrentData(newData);
      } else {
        setCurrentData((prev) => [...prev, ...newData]);
      }

      setHasMore(pageNum < 5);
      setLoading(false);
    },
    [],
  );
  useEffect(() => {
    setPage(1);
    loadData(1, true);
  }, [activeTab, loadData]);
  useEffect(() => {
    const handleScroll = () => {
      if (loading || !hasMore) return;

      const scrollTop = document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;

      if (scrollTop + clientHeight >= scrollHeight - 100) {
        const nextPage = page + 1;
        setPage(nextPage);
        loadData(nextPage, false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [loading, hasMore, page, loadData]);
  const handlePurchase = (id: string) => {
    alert(`认购产品 ID: ${id}`);
  };

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    updateSearchParams({ type: tab });
    setStatusFilter("all");
    setSearchQuery("");
  };

  const filteredData = getFilteredData(currentData);
  useEffect(() => {
    if (!searchParams.get("type")) {
      updateSearchParams({ type: activeTab });
    }
  }, []);
  useEffect(() => {
    const urlUpdateParams: any = {
      ...urlParams,
      viewMode: viewMode || "card",
      statusFilter: statusFilter || "all",
      search: searchQuery || "",
    };
    if (activeTab === "current") {
      delete urlUpdateParams.statusFilter;
      delete urlUpdateParams.viewMode;
    }
    updateSearchParams(urlUpdateParams);
  }, [viewMode, statusFilter, searchQuery]);

  return (
    <div className="flex flex-col min-h-screen">
      <div className="fixed top-0 left-0 right-0 z-50">
        <Header />
      </div>
      <div className="flex-1 mt-[80px] pt-4 overflow-y-auto scrollbar-hide px-[112px]">
        <div className="mb-6">
          <nav className="flex space-x-4">
            {(["current", "past"] as TabType[]).map((tab) => (
              <button
                key={tab}
                onClick={() => handleTabChange(tab)}
                className={`font-semibold text-display-xs transition-colors cursor-pointer ${
                  activeTab === tab
                    ? "text-text-primary"
                    : " text-text-quaternary hover:text-text-primary"
                }`}
              >
                {tradeTypeTabs.find((t) => t.id === tab)?.label}
              </button>
            ))}
          </nav>
        </div>

        {/* 工具栏 */}
        <div className="mb-[38px] flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            {activeTab === "past" && (
              <Tabs
                selectedKey={statusFilter}
                onSelectionChange={(key) => setStatusFilter(key as any)}
                className="w-max"
              >
                <Tabs.List type="button-border" items={tradeTabs}>
                  {(tab) => <Tabs.Item {...tab} />}
                </Tabs.List>
              </Tabs>
            )}

            <div className="relative flex-1 w-full">
              <Input
                size="md"
                aria-label="Search"
                placeholder="Search"
                icon={SearchLg}
                value={searchQuery}
                onChange={(value) => setSearchQuery(value)}
                className={"w-full border-border-secondary "}
                wrapperClassName="bg-bg-secondary_alt"
              />
            </div>
          </div>

          {activeTab === "past" && (
            <Tabs
              selectedKey={viewMode}
              onSelectionChange={(key) => setViewMode(key as any)}
              className="w-max"
            >
              <Tabs.List type="button-border" items={viewTabs}>
                {(tab) => <Tabs.Item {...tab} className={"py-1.5"} />}
              </Tabs.List>
            </Tabs>
          )}
        </div>

        {/* 数据展示区域 */}
        <div className="mb-8">
          {filteredData.length === 0 && !loading ? (
            <div className="text-center py-12">
              <div className="text-gray-500">暂无符合条件的产品</div>
            </div>
          ) : viewMode === "card" ? (
            <div className="grid grid-cols-2 gap-6">
              {filteredData.map((item) => (
                <TradingCard
                  key={item.id}
                  data={item}
                  onPurchase={handlePurchase}
                />
              ))}
            </div>
          ) : (
            <></>
          )}
        </div>

        {/* 加载状态 */}
        {loading && (
          <div className="text-center py-8">
            <div className="inline-flex items-center gap-2 text-gray-600">
              <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
              加载中...
            </div>
          </div>
        )}

        {/* 没有更多数据提示 */}
        {!loading && !hasMore && filteredData.length > 0 && (
          <div className="text-center py-8 text-gray-500">已加载全部数据</div>
        )}
      </div>
      <div className="mt-auto">
        <Footer />
      </div>
    </div>
  );
};

export default Trade;
