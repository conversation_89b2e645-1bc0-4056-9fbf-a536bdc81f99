import type { Metadata, Viewport } from "next";
import localFont from "next/font/local";
import { RouteProvider } from "@/providers/route-provider";
import { ThemeProvider } from "@/providers/theme-provider";
import "./globals.css";
const pingfang = localFont({
  src: [
    {
      path: "../../public/fonts/PingFangSC-Ultralight.woff2",
      weight: "100",
      style: "normal",
    },
    {
      path: "../../public/fonts/PingFangSC-Thin.woff2",
      weight: "200",
      style: "normal",
    },
    {
      path: "../../public/fonts/PingFangSC-Light.woff2",
      weight: "300",
      style: "normal",
    },
    {
      path: "../../public/fonts/PingFangSC-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/PingFangSC-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "../../public/fonts/PingFangSC-Semibold.woff2",
      weight: "600",
      style: "normal",
    },
  ],
  display: "swap",
  variable: "--font-pingfang",
  fallback: ["PingFang SC", "-apple-system", "sans-serif"],
  preload: true,
});

export const metadata: Metadata = {
  title: "EPOCH RWA",
  description: "EPOCH RWA",
};

export const viewport: Viewport = {
  colorScheme: "light",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${pingfang.variable} scroll-smooth`}>
      <body className="bg-primary antialiased">
        <RouteProvider>
          <ThemeProvider>{children}</ThemeProvider>
        </RouteProvider>
      </body>
    </html>
  );
}
