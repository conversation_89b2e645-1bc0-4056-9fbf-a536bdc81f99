@import "tailwindcss";
@import "../../public/styles/theme.css";

@plugin "tailwindcss-animate";
@plugin "tailwindcss-react-aria-components";

@custom-variant dark (&:where(.dark-mode, .dark-mode *));
@custom-variant label (& [data-label]);
@custom-variant focus-input-within (&:has(input:focus));

@utility scrollbar-hide {
  /* For Webkit-based browsers (Chrome, Safari and Opera) */
  &::-webkit-scrollbar {
    display: none;
    -webkit-appearance: none;
  }

  /* For IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

@utility transition-inherit-all {
  transition-property: inherit;
  transition-duration: inherit;
  transition-timing-function: inherit;
}
html,
body {
  font-family: var(--font-body);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-variant-ligatures: contextual;
  font-variant-ligatures: contextual;
  -webkit-font-kerning: normal;
  font-kerning: normal;
}

/* Hide the default expand arrow on Safari.  */
details summary::-webkit-details-marker {
  display: none;
}

/* Hide default arrows from number inputs. */
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

/* Hide the default clear button (X) from search inputs. */
input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
