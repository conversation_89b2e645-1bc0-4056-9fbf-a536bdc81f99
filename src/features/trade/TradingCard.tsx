import React from "react";
import { Badge } from "@/components/base/badges/badges";
import { TradingCardData } from "@/types/interface";
import { CardBadge } from "../CardBadge";
import { Avatar } from "@/components/base/avatar/avatar";
import Usdc from "@/icons/Usdc";
import Usdt from "@/icons/Usdt";
import Eusd from "@/icons/Eusd";
import Usd from "@/icons/Usd";
import Cny from "@/icons/Cny";
import { Button } from "@/components/base/buttons/button";
import { twMerge } from "tailwind-merge";

interface TradingCardProps {
  data: TradingCardData;
  onPurchase?: (id: string) => void;
  className?: string;
}

const CurrencyIcon: React.FC<{ currency: string }> = ({ currency }) => {
  const iconMap: Record<string, React.ReactNode> = {
    USDC: <Usdc />,
    CNY: <Cny />,
    EUSD: <Eusd />,
    USDT: <Usdt />,
    USD: <Usd />,
  };

  return (
    <Avatar
      size="xxs"
      alt={currency}
      tooltipTitle={currency}
      className="ring-[1.5px] ring-bg-primary"
      placeholder={iconMap[currency]}
    />
  );
};

const ProgressRing: React.FC<{ progress: number; size?: number }> = ({
  progress,
  size = 80,
}) => {
  const radius = (size - 8) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg width={size} height={size} className="transform -rotate-90">
        {/* 背景环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="var(--color-bg-tertiary)"
          strokeWidth="8"
          fill="transparent"
        />
        {/* 进度环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="var(--color-border-brand)"
          strokeWidth="8"
          fill="transparent"
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          className="transition-all duration-300 ease-in-out"
        />
      </svg>
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <span className="text-xs font-medium text-text-tertiary">
          {progress}%
        </span>
      </div>
    </div>
  );
};

const RateAndLockPeriod: React.FC<{
  rate: string;
  lockPeriod: string;
  className?: string;
}> = ({ rate, lockPeriod, className }) => (
  <div className={twMerge("flex flex-col items-end", className)}>
    <span className="text-3xl font-semibold text-orange-500 mb-2">{rate}</span>
    <span className="text-sm font-regular text-text-quaternary whitespace-nowrap">
      锁定期 {lockPeriod}
    </span>
  </div>
);

export const TradingCard: React.FC<TradingCardProps> = ({
  data,
  onPurchase,
  className = "",
}) => {
  const {
    id,
    issuer,
    assetType,
    scale,
    duration,
    lockPeriod,
    status,
    rate,
    targetAmount,
    unitPrice,
    remaining,
    remainingShares,
    deadline,
    progress,
    tags,
    supportedCurrencies,
  } = data;

  const handlePurchase = () => {
    onPurchase?.(id);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div
      className={`relative bg-primary rounded-bl-4xl rounded-tr-4xl shadow-sm border border-border-secondary 
  transition-all duration-200 
  hover:translate-y-[-3px] hover:shadow-sm hover:outline-[3px] hover:outline-border-secondary ${className}`}
    >
      <div className="px-0 absolute top-[-14px] left-0">
        <CardBadge status={status} />
      </div>

      <div className="px-6 pt-7 pb-6 flex flex-col gap-6">
        <div className="flex items-start justify-between gap-4">
          <div className="flex flex-col gap-[10px]">
            <div className="text-lg font-semibold text-text-quaternary line-clamp-1">
              {issuer} {assetType} {scale} {duration} {lockPeriod}
            </div>

            <div className="flex flex-wrap gap-2.5">
              {tags.map((tag, index) => (
                <Badge key={index} color={tag.color} size="md" type="color">
                  {tag.label}
                </Badge>
              ))}
            </div>
          </div>
          {status === "current" && (
            <RateAndLockPeriod rate={rate} lockPeriod={lockPeriod} />
          )}
        </div>

        {status === "current" ? (
          <>
            <div className="flex justify-between gap-2 px-4 py-[14px] rounded-2xl bg-bg-secondary">
              <div className="flex flex-col gap-2 items-start justify-center">
                <div className="flex items-center gap-2.5">
                  <span className="font-regular text-sm text-text-quaternary">
                    募集目标
                  </span>
                  <span className="font-medium text-sm text-text-primary">
                    {formatCurrency(targetAmount)}
                  </span>
                </div>

                <div className="flex items-center gap-2.5">
                  <span className="font-regular text-sm text-text-quaternary">
                    每份单价
                  </span>
                  <span className="font-medium text-sm text-text-primary">
                    {formatCurrency(unitPrice)}
                  </span>
                </div>

                <div className="flex items-center gap-2.5">
                  <span className="font-regular text-sm text-text-quaternary">
                    剩余
                  </span>
                  <span className="font-medium text-sm text-text-primary">
                    {formatCurrency(remaining)} ({progress}%, {remainingShares}
                    份)
                  </span>
                </div>

                <div className="flex items-center gap-2.5">
                  <span className="font-regular text-sm text-text-quaternary">
                    认购截止
                  </span>
                  <span className="font-medium text-sm text-text-primary">
                    {deadline}
                  </span>
                </div>
              </div>
              <div className="flex flex-col items-center justify-center gap-2.5 pt-2">
                <ProgressRing progress={progress} size={60} />
                <span className="text-sm font-regular text-text-quaternary px-0.5 pb-1.5">
                  认购进度
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center space-x-2.5">
                <span className="text-sm font-regular text-text-quaternary">
                  支持
                </span>
                <div className="flex -space-x-1">
                  {supportedCurrencies.map((currency) => (
                    <CurrencyIcon key={currency} currency={currency} />
                  ))}
                </div>
              </div>
              <Button
                size="sm"
                className={`w-[264px] py-2.5 px-4 rounded-lg text-md font-semibold bg-orange-500 hover:bg-orange-600 text-white`}
                onClick={handlePurchase}
              >
                认购
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="flex flex-col items-start justify-center gap-2">
              <div className="flex items-center gap-2.5">
                <span className="font-regular text-sm text-text-quaternary">
                  资产总值
                </span>
                <span className="font-medium text-sm text-text-primary">
                  {formatCurrency(targetAmount)}
                </span>
              </div>
              <div className="flex items-center gap-2.5">
                <span className="font-regular text-sm text-text-quaternary">
                  到期日
                </span>
                <span className="font-medium text-sm text-text-primary">
                  {deadline}
                </span>
              </div>
            </div>
            <RateAndLockPeriod
              rate={rate}
              lockPeriod={lockPeriod}
              className="absolute bottom-6 right-6 "
            />
          </>
        )}
      </div>
    </div>
  );
};

export default TradingCard;
