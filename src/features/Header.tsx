import { Button } from "@/components/base/buttons/button";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Favicon } from "./Favicon";

const Header = () => {
  const pathname = usePathname();
  const navItems = [
    { label: "首页", href: "/" },
    { label: "资产交易", href: "/trade" },
    { label: "我的投资", href: "/portfolio" },
  ];
  return (
    <div className="w-full h-[80px] bg-primary py-[18px] flex items-center px-28 justify-between z-20">
      <div className="flex items-center">
        <Favicon />
        <nav className="flex gap-0.5 text-text-secondary font-semibold text-md ml-16 my-0.5">
          {navItems.map((item) => {
            const isActive =
              item.href === "/"
                ? pathname === "/"
                : pathname.startsWith(item.href);

            return (
              <Link
                key={item.href}
                href={item.href}
                className={`transition-colors px-3 py-2 rounded-md cursor-pointer ${
                  isActive
                    ? "text-text-secondary_hover bg-bg-secondary_hover"
                    : " hover:text-secondary_hover hover:bg-bg-primary_hover"
                }`}
              >
                {item.label}
              </Link>
            );
          })}
        </nav>
      </div>

      <div className="flex gap-3">
        <Button color="secondary" size="lg">
          登录
        </Button>
        <Button color="primary" size="lg">
          注册
        </Button>
      </div>
    </div>
  );
};

export default Header;
