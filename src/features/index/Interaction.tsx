import { TradingCardData } from "@/types/interface";
import { ChevronRight } from "@untitledui/icons";
import TradingCard from "../trade/TradingCard";

export const Interaction = () => {
  const mockTradingData1: TradingCardData[] = [
    {
      id: "1",
      issuer:
        "A 股上市跨境电商第一股，年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿",
      assetType: "AAA",
      scale: "资产类型",
      duration: "规模 时间",
      lockPeriod: "3 个月",
      status: "募集中",
      rate: "7.8%",
      targetAmount: 10000,
      unitPrice: 10,
      remaining: 4000,
      remainingShares: 400,
      deadline: "2025 年 8 月 10 日",
      progress: 60,
      tags: [
        { label: "AAA", color: "brand" },
        { label: "贸易融资", color: "blue" },
        { label: "低风险", color: "success" },
      ],
      supportedCurrencies: ["USD", "CNY", "EUSD", "USDC"],
    },
    {
      id: "2",
      issuer: "华融集团",
      assetType: "BB+",
      scale: "供应链金融",
      duration: "中等规模",
      lockPeriod: "6 个月",
      status: "募集中",
      rate: "9.2%",
      targetAmount: 50000,
      unitPrice: 25,
      remaining: 15000,
      remainingShares: 600,
      deadline: "2025 年 9 月 15 日",
      progress: 70,
      tags: [
        { label: "BB+", color: "brand" },
        { label: "供应链", color: "blue" },
        { label: "中风险", color: "success" },
      ],
      supportedCurrencies: ["USD", "CNY"],
    },
    {
      id: "3",
      issuer: "招商银行",
      assetType: "AAA",
      scale: "结构化产品",
      duration: "大规模",
      lockPeriod: "12 个月",
      status: "募集中",
      rate: "6.5%",
      targetAmount: 100000,
      unitPrice: 50,
      remaining: 50,
      remainingShares: 1,
      deadline: "2025 年 7 月 30 日",
      progress: 99,
      tags: [
        { label: "AAA", color: "brand" },
        { label: "结构化", color: "blue" },
        { label: "低风险", color: "success" },
      ],
      supportedCurrencies: ["USD", "CNY", "EUSD", "USDC", "USDT"],
    },
    {
      id: "4",
      issuer:
        "平安信托年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿",
      assetType: "A+",
      scale: "房地产基金",
      duration: "中等规模",
      lockPeriod: "18 个月",
      status: "募集中",
      rate: "8.5%",
      targetAmount: 75000,
      unitPrice: 100,
      remaining: 75000,
      remainingShares: 750,
      deadline: "2025 年 10 月 1 日",
      progress: 0,
      tags: [
        { label: "A+", color: "brand" },
        { label: "房地产", color: "blue" },
        { label: "中风险", color: "success" },
      ],
      supportedCurrencies: ["USD", "CNY", "EUSD"],
    },
  ];
  const mockTradingData2: TradingCardData[] = [
    {
      id: "1",
      issuer:
        "A 股上市跨境电商第一股，年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿",
      assetType: "AAA",
      scale: "资产类型",
      duration: "规模 时间",
      lockPeriod: "3 个月",
      status: "已完结",
      rate: "7.8%",
      targetAmount: 10000,
      unitPrice: 10,
      remaining: 4000,
      remainingShares: 400,
      deadline: "2025 年 8 月 10 日",
      progress: 60,
      tags: [
        { label: "AAA", color: "brand" },
        { label: "贸易融资", color: "blue" },
        { label: "低风险", color: "success" },
      ],
      supportedCurrencies: ["USD", "CNY", "EUSD", "USDC"],
    },
    {
      id: "2",
      issuer: "华融集团",
      assetType: "BB+",
      scale: "供应链金融",
      duration: "中等规模",
      lockPeriod: "6 个月",
      status: "运作中",
      rate: "9.2%",
      targetAmount: 50000,
      unitPrice: 25,
      remaining: 15000,
      remainingShares: 600,
      deadline: "2025 年 9 月 15 日",
      progress: 70,
      tags: [
        { label: "BB+", color: "brand" },
        { label: "供应链", color: "blue" },
        { label: "中风险", color: "success" },
      ],
      supportedCurrencies: ["USD", "CNY"],
    },
    {
      id: "3",
      issuer: "招商银行",
      assetType: "AAA",
      scale: "结构化产品",
      duration: "大规模",
      lockPeriod: "12 个月",
      status: "运作中",
      rate: "6.5%",
      targetAmount: 100000,
      unitPrice: 50,
      remaining: 0,
      remainingShares: 0,
      deadline: "2025 年 7 月 30 日",
      progress: 100,
      tags: [
        { label: "AAA", color: "brand" },
        { label: "结构化", color: "blue" },
        { label: "低风险", color: "success" },
      ],
      supportedCurrencies: ["USD", "CNY", "EUSD", "USDC", "USDT"],
    },
    {
      id: "4",
      issuer:
        "平安信托年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿年销售过百亿",
      assetType: "A+",
      scale: "房地产基金",
      duration: "中等规模",
      lockPeriod: "18 个月",
      status: "已完结",
      rate: "8.5%",
      targetAmount: 75000,
      unitPrice: 100,
      remaining: 75000,
      remainingShares: 750,
      deadline: "2025 年 10 月 1 日",
      progress: 0,
      tags: [
        { label: "A+", color: "brand" },
        { label: "房地产", color: "blue" },
        { label: "中风险", color: "success" },
      ],
      supportedCurrencies: ["USD", "CNY", "EUSD"],
    },
  ];
  const handlePurchase = (id: string) => {
    console.log(`购买产品 ID: ${id}`);
  };
  return (
    <div className="w-full py-24 px-[112px] flex flex-col gap-24">
      <div className="flex flex-col gap-[46px]">
        <div className="flex justify-between">
          <span className="text-text-primary font-semibold text-display-md">
            募集中
          </span>
          <div className="flex p-2 cursor-pointer size-10 justify-center items-center rounded-full border border-border-secondary shadow-sm bg-primary">
            <ChevronRight
              className="text-fg-secondary"
              size={24}
              strikethroughThickness={2}
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-x-8 gap-y-10">
          {mockTradingData1.map((product) => (
            <TradingCard
              key={product.id}
              data={product}
              onPurchase={handlePurchase}
              className="hover:scale-[1.02] transition-transform duration-200"
            />
          ))}
        </div>
      </div>
      <div className="flex flex-col gap-[46px]">
        <div className="flex justify-between">
          <span className="text-text-primary font-semibold text-display-md">
            往期
          </span>
          <div className="flex p-2 cursor-pointer size-10 justify-center items-center rounded-full border border-border-secondary shadow-sm bg-primary">
            <ChevronRight
              className="text-fg-secondary"
              size={24}
              strikethroughThickness={2}
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-x-8 gap-y-10">
          {mockTradingData2.map((product) => (
            <TradingCard
              key={product.id}
              data={product}
              onPurchase={handlePurchase}
              className="hover:scale-[1.02] transition-transform duration-200"
            />
          ))}
        </div>
      </div>
    </div>
  );
};
