import { Button } from "@/components/base/buttons/button";
import Image from "next/image";

export const Banner = () => {
  return (
    <div
      className="w-full h-[710px] px-20 pt-16 pb-24 bg-center bg-cover "
      style={{
        backgroundImage: `
      linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0) 40%, rgba(255,255,255,0) 60%, rgba(255,255,255,1)),
      url('/images/Banner_bg.svg')
    `,
      }}
    >
      <div className="w-full h-full px-8 gap-8 items-center flex ">
        <div className="flex flex-col items-start">
          <p className="text-text-primary font-semibold text-6xl">Epoch RWA </p>
          <p className="text-text-primary font-semibold text-6xl mb-6">
            开启 RWA 交易新纪元
          </p>
          <p className="text-text-tertiary font-regular text-xl mb-12">
            连接现实与虚拟、增强流动性、提升透明度、创造效率、开启新篇章。​告别沉睡的资产价值，让
            Epoch RWA 为您的资产插上流动与创新的翅膀。
          </p>
          <div className="flex gap-3 items-center">
            <Button color="secondary" size="xl">
              联系我们
            </Button>
            <Button color="primary" size="xl">
              注册
            </Button>
          </div>
        </div>
        <div style={{ width: 580, height: 550 }} className="shrink-0">
          <Image
            src="/images/Banner.svg"
            alt="banner"
            width={580}
            height={550}
            className="!w-[580px] !h-[550px]"
            unoptimized
          />
        </div>
      </div>
    </div>
  );
};
