import { Button } from "@/components/base/buttons/button";
import { Favicon } from "../Favicon";

export const Footer = ({ isHome = false }: { isHome?: boolean }) => {
  return (
    <footer className="bg-primary px-[112px]">
      <div className="mx-auto flex flex-col">
        {isHome && (
          <div className="bg-brand-tertiary-04 rounded-2xl p-16 flex justify-between mb-24">
            <div className="flex flex-col justify-start gap-2">
              <p className="text-text-primary font-semibold text-display-sm">
                立即开始 RWA 投资之旅
              </p>
              <p className="text-text-quaternary font-regular text-display-xs">
                开启 RWA 交易新纪元
              </p>
            </div>
            <div className="flex gap-4">
              <Button color="secondary" size="xl">
                联系我们
              </Button>

              <Button color="primary" size="xl">
                注册
              </Button>
            </div>
          </div>
        )}
        <div className="flex items-center justify-between py-9 border-t border-border-secondary">
          <Favicon />
          <p className="text-text-quaternary text-md font-regular">
            © 2077 Epoch RWA. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};
