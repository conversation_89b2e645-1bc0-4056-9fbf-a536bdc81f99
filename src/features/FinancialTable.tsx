import React, { useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  createColumnHelper,
} from "@tanstack/react-table";

// 数据类型定义
export interface FinancialProduct {
  id: string;
  name: string;
  rating: "AAA" | "AA" | "A" | "BBB";
  category: "贸易融资" | "供应链金融" | "消费金融" | "小微金融";
  riskLevel: "低风险" | "中风险" | "高风险";
  amount: number;
  maturityDate: string;
  lockPeriod: string;
  yield: number;
}

const columnHelper = createColumnHelper<FinancialProduct>();

// 模拟数据
const mockData: FinancialProduct[] = Array.from({ length: 9 }, (_, index) => ({
  id: `product-${index + 1}`,
  name: "A股上市跨境电商第一股，年销售过百亿...",
  rating: "AAA",
  category: "贸易融资",
  riskLevel: "低风险",
  amount: 10000,
  maturityDate: "2025年8月10日",
  lockPeriod: "3个月",
  yield: 7.8,
}));

const FinancialTable: React.FC<{ data?: FinancialProduct[] }> = ({
  data = mockData,
}) => {
  const columns = useMemo(
    () => [
      columnHelper.accessor("name", {
        header: "资产名称",
        cell: (info) => (
          <div className="max-w-[280px] truncate text-gray-800 font-medium">
            {info.getValue()}
          </div>
        ),
      }),
      columnHelper.accessor("rating", {
        header: "属性",
        cell: (info) => (
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200">
              {info.row.original.rating}
            </span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-700 border border-blue-200">
              {info.row.original.category}
            </span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
              {info.row.original.riskLevel}
            </span>
          </div>
        ),
      }),
      columnHelper.accessor("amount", {
        header: "规模",
        cell: (info) => (
          <div className="text-gray-800 font-medium">
            ${info.getValue().toLocaleString()}
          </div>
        ),
      }),
      columnHelper.accessor("maturityDate", {
        header: "到期日",
        cell: (info) => <div className="text-gray-600">{info.getValue()}</div>,
      }),
      columnHelper.accessor("lockPeriod", {
        header: "锁定期",
        cell: (info) => <div className="text-gray-600">{info.getValue()}</div>,
      }),
      columnHelper.accessor("yield", {
        header: "收益",
        cell: (info) => (
          <div className="text-orange-600 font-semibold">
            {info.getValue()}%
          </div>
        ),
      }),
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="w-full bg-white">
      {/* 表格容器 */}
      <div className="overflow-x-auto">
        <div className="w-full grid grid-cols-[340px_248px_270px_172px_79px_1fr] px-[28px] pb-2 text-left text-sm font-medium text-gray-500 ">
          {table.getHeaderGroups().map((headerGroup) =>
            headerGroup.headers.map((header) => (
              <div key={header.id} className="py-1 pl-4">
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
              </div>
            ))
          )}
        </div>
        <table className="w-full border-separate border-spacing-y-3 rounded-2xl border border-amber-200">
          {/* 表格主体 */}
          <tbody className="">
            {table.getRowModel().rows.map((row, index) => (
              <tr
                key={row.id}
                className={`mx-3
          grid grid-cols-[358px_248px_270px_172px_79px_1fr] rounded-2xl
          hover:bg-gray-50 transition-colors duration-150 ease-in-out cursor-pointer
          ${index % 2 === 0 ? "bg-white" : "bg-gray-50/30"}
        `}
              >
                {row.getVisibleCells().map((cell) => (
                  <td
                    key={cell.id}
                    className="px-6 py-4 whitespace-nowrap text-sm"
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 如果没有数据显示空状态 */}
      {data.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg">暂无数据</div>
        </div>
      )}
    </div>
  );
};

export default FinancialTable;
