import React from "react";
import clsx from "clsx";
import { badgeMap } from "@/types/trade";
import { tradeStatus } from "@/types/interface";

interface CardBadgeProps {
  status: tradeStatus;
}

const badgeStyles: Record<tradeStatus, string> = {
  current: "bg-gradient-to-r from-green-400 to-green-600",
  ongoing: "bg-gradient-to-r from-purple-400 to-purple-600",
  over: "bg-gradient-to-r from-gray-400 to-gray-600",
};

export const CardBadge: React.FC<CardBadgeProps> = ({ status }) => {
  return (
    <span
      className={clsx(
        "inline-flex items-center px-6 py-1 text-sm font-medium text-white rounded-tl-lg rounded-br-lg",
        badgeStyles[status],
      )}
    >
      {badgeMap[status]}
    </span>
  );
};
