"use client";

import { useRouter, useSearchParams } from "next/navigation";

import { TabType, ViewMode } from "@/types/trade";
import { StatusFilter } from "@/types/interface";

interface ISearchParams {
  viewMode?: ViewMode;
  type?: TabType;
  search?: string;
  statusFilter?: StatusFilter;
}

export function useUpdateSearchParams(tabPrefix?: string) {
  const router = useRouter();

  const updateSearchParams = (updates: ISearchParams) => {
    const params = new URLSearchParams();

    Object.entries(updates).forEach(([key, value]) => {
      const paramKey =
        tabPrefix && isFilterParam(key) ? `${tabPrefix}_${key}` : key;
      if (value) {
        params.set(paramKey, String(value));
      }
    });

    router.replace(`?${params.toString()}`, { scroll: false });
  };

  return updateSearchParams;
}

function isFilterParam(key: string): boolean {
  const filterParams = ["search", "statusFilter", "viewMode"];
  return filterParams.includes(key);
}

export function useReadSearchParams(tabPrefix?: string) {
  const searchParams = useSearchParams();

  const readSearchParams = (): ISearchParams => {
    const params: ISearchParams = {};

    const getParam = (key: string) => {
      if (tabPrefix && isFilterParam(key)) {
        return searchParams.get(`${tabPrefix}_${key}`);
      }
      return searchParams.get(key);
    };

    const search = getParam("search");
    if (search) params.search = search;

    const statusFilter = getParam("statusFilter");
    if (statusFilter) params.statusFilter = statusFilter as StatusFilter;

    const viewMode = getParam("viewMode");
    if (viewMode) params.viewMode = viewMode as ViewMode;

    return params;
  };

  return readSearchParams;
}
