export interface TradingCardData {
  id: string;
  issuer: string;
  assetType: string;
  scale: string;
  duration: string;
  lockPeriod: string;
  status: tradeStatus;
  rate: string;
  targetAmount: number;
  unitPrice: number;
  remaining: number;
  remainingShares: number;
  deadline: string;
  progress: number; // 0-100
  tags: Array<{
    label: string;
    color: "blue" | "success" | "brand";
  }>;
  supportedCurrencies: string[];
}

export type StatusFilter = "all" | "ongoing" | "over";
export type tradeStatus = "current" | "ongoing" | "over";
