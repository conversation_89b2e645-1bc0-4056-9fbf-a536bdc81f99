import React from "react";

import { twMerge } from "tailwind-merge";

import { IconSvgProps } from "@/types/IconSvgProps";

const Usd = (props: IconSvgProps) => {
  const { className = "" } = props;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={twMerge(className)}
    >
      <circle cx="12" cy="11.9999" r="12" fill="#F2F0F2" />
      <mask
        id="mask0_11222_73"
        style={{ maskType: "alpha" }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="24"
        height="24"
      >
        <circle cx="12" cy="12" r="12" fill="#FCFCFC" />
      </mask>
      <g mask="url(#mask0_11222_73)">
        <rect x="3.89966" y="21.2999" width="16.5" height="3" fill="#D90026" />
        <rect x="3.89966" y="21.2999" width="16.5" height="3" fill="#D90026" />
        <rect x="0.299805" y="15.2999" width="23.7" height="3" fill="#D90026" />
        <rect x="0.299805" y="15.2999" width="23.7" height="3" fill="#D90026" />
        <rect x="0.299805" y="8.99982" width="23.7" height="3" fill="#D90026" />
        <rect x="0.299805" y="8.99982" width="23.7" height="3" fill="#D90026" />
        <rect x="0.299805" y="2.99994" width="23.7" height="3" fill="#D90026" />
        <rect x="0.299805" y="2.99994" width="23.7" height="3" fill="#D90026" />
        <rect width="12.3" height="12" fill="#0052B5" />
        <path
          d="M9.89995 10.0498L8.99995 10.4998L9.29995 9.59979L8.69995 8.99979H9.59995L9.89995 8.09979L10.35 8.99979H11.1L10.5 9.59979L10.8 10.4998L9.89995 10.0498Z"
          fill="#EFEFEF"
        />
        <path
          d="M5.69988 10.0498L4.79988 10.4998L5.09988 9.59979L4.49988 8.99979H5.39988L5.69988 8.09979L6.14988 8.99979H6.89988L6.29988 9.59979L6.59988 10.4998L5.69988 10.0498Z"
          fill="#EFEFEF"
        />
        <path
          d="M1.4998 10.0498L0.599805 10.4998L0.899805 9.59979L0.299805 8.99979H1.1998L1.4998 8.09979L1.9498 8.99979H2.6998L2.0998 9.59979L2.3998 10.4998L1.4998 10.0498Z"
          fill="#EFEFEF"
        />
        <path
          d="M9.89995 6.74974L8.99995 7.19974L9.29995 6.29974L8.69995 5.69974H9.59995L9.89995 4.79974L10.35 5.69974H11.1L10.5 6.29974L10.8 7.19974L9.89995 6.74974Z"
          fill="#EFEFEF"
        />
        <path
          d="M9.89995 3.44976L8.99995 3.89976L9.29995 2.99976L8.69995 2.39976H9.59995L9.89995 1.49976L10.35 2.39976H11.1L10.5 2.99976L10.8 3.89976L9.89995 3.44976Z"
          fill="#EFEFEF"
        />
        <path
          d="M9.89995 0.149707L8.99995 0.599707L9.29995 -0.300293L8.69995 -0.900293H9.59995L9.89995 -1.80029L10.35 -0.900293H11.1L10.5 -0.300293L10.8 0.599707L9.89995 0.149707Z"
          fill="#EFEFEF"
        />
        <path
          d="M5.69988 3.44976L4.79988 3.89976L5.09988 2.99976L4.49988 2.39976H5.39988L5.69988 1.49976L6.14988 2.39976H6.89988L6.29988 2.99976L6.59988 3.89976L5.69988 3.44976Z"
          fill="#EFEFEF"
        />
        <path
          d="M5.69988 6.74974L4.79988 7.19974L5.09988 6.29974L4.49988 5.69974H5.39988L5.69988 4.79974L6.14988 5.69974H6.89988L6.29988 6.29974L6.59988 7.19974L5.69988 6.74974Z"
          fill="#EFEFEF"
        />
        <path
          d="M1.4998 6.74974L0.599805 7.19974L0.899805 6.29974L0.299805 5.69974H1.1998L1.4998 4.79974L1.9498 5.69974H2.6998L2.0998 6.29974L2.3998 7.19974L1.4998 6.74974Z"
          fill="#EFEFEF"
        />
      </g>
    </svg>
  );
};

export default Usd;
