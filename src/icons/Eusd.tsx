import React from "react";

import { twMerge } from "tailwind-merge";

import { IconSvgProps } from "@/types/IconSvgProps";

const Eusd = (props: IconSvgProps) => {
  const { className = "" } = props;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={twMerge(className)}
    >
      <rect width="24" height="24" rx="12" fill="#7A5AF8" />
      <rect
        x="2.20178"
        y="2.20178"
        width="19.5963"
        height="19.5963"
        rx="9.79816"
        fill="#7A5AF8"
      />
      <path
        d="M5.28394 12.1131C5.28394 9.87578 5.28394 7.63781 5.28394 5.40044C5.28394 5.24459 5.2973 5.22937 5.45097 5.22937C7.50642 5.22937 9.56187 5.22937 11.6173 5.22937C11.7667 5.22937 11.7698 5.24276 11.7686 5.39375C11.7637 5.87531 11.7655 6.35688 11.7649 6.83784C11.7649 7.21652 11.7649 7.5958 11.7649 7.97448C11.7649 8.12851 11.7516 8.1419 11.5948 8.1419C10.524 8.1419 9.45315 8.1419 8.38169 8.14251C8.19643 8.14251 8.19218 8.14677 8.19218 8.33733C8.19218 8.92117 8.19218 9.50563 8.19218 10.0895C8.19218 10.2313 8.19643 10.3732 8.18307 10.5138C8.17274 10.6246 8.2183 10.6526 8.3167 10.652C8.65138 10.6496 8.98605 10.652 9.32073 10.652C10.1006 10.652 10.8799 10.652 11.6598 10.652C11.8135 10.652 11.8184 10.6538 11.8214 10.8091C11.839 11.6772 11.8293 12.5448 11.8293 13.4129C11.8293 13.5566 11.7783 13.5676 11.6683 13.567C10.609 13.5645 9.54972 13.5652 8.49102 13.5645C8.29179 13.5645 8.19218 13.6622 8.19218 13.8574C8.19218 14.493 8.19218 15.128 8.19218 15.7636C8.19218 15.8068 8.19279 15.8506 8.18975 15.8938C8.17699 16.074 8.17639 16.0753 8.36286 16.0753C9.43007 16.0753 10.4973 16.0753 11.5639 16.0753C11.7716 16.0753 11.7667 16.0753 11.7661 16.2835C11.7637 17.1084 11.7649 17.9333 11.7649 18.7583C11.7649 18.7778 11.7649 18.7978 11.7649 18.8173C11.7643 18.9823 11.7588 18.9884 11.59 18.9884C10.1529 18.9884 8.71576 18.9884 7.27865 18.9884C6.6761 18.9884 6.07356 18.9884 5.47162 18.9884C5.29426 18.9884 5.28576 18.9799 5.28576 18.8009C5.28576 16.5708 5.28576 14.3414 5.28576 12.1113H5.28394V12.1131Z"
        fill="white"
      />
      <path
        d="M15.5041 18.9908C14.5711 18.9908 13.6387 18.9908 12.7057 18.9908C12.5247 18.9908 12.5168 18.9829 12.5168 18.8063C12.5168 17.9503 12.5168 17.0943 12.5168 16.2384C12.5168 16.091 12.5296 16.0776 12.6802 16.0776C13.5895 16.0776 14.4988 16.0776 15.4075 16.0776C15.5891 16.0776 15.5885 16.0776 15.5885 15.9011C15.5885 15.3806 15.5903 14.86 15.5885 14.3395C15.5885 14.261 15.6261 14.2323 15.6936 14.2281C15.7525 14.225 15.8114 14.2269 15.8703 14.2269C16.6848 14.2269 17.5 14.2269 18.3145 14.2269C18.4858 14.2269 18.4955 14.2366 18.4955 14.4071C18.4955 15.8664 18.4955 17.3263 18.4955 18.7856C18.4955 18.9877 18.4931 18.9902 18.2908 18.9902C17.3621 18.9902 16.4334 18.9902 15.5041 18.9902V18.9908Z"
        fill="white"
      />
      <path
        d="M15.5096 5.22937C16.4469 5.22937 17.3835 5.22937 18.3207 5.22937C18.4853 5.22937 18.4956 5.23972 18.4956 5.40288C18.4956 6.87497 18.4956 8.34646 18.4956 9.81856C18.4956 9.97928 18.4829 9.99207 18.3195 9.99207C17.4655 9.99207 16.6109 9.99207 15.7569 9.99207C15.5965 9.99207 15.591 9.97563 15.5929 9.81369C15.5965 9.44353 15.5904 9.07277 15.5892 8.702C15.5892 8.56807 15.5923 8.43352 15.5892 8.29958C15.585 8.15103 15.5746 8.14251 15.4216 8.14251C14.8549 8.1419 14.2875 8.14251 13.7208 8.14251C13.3783 8.14251 13.0357 8.14312 12.6931 8.14251C12.534 8.14251 12.5176 8.12486 12.5176 7.96535C12.5176 7.10937 12.5176 6.25277 12.5176 5.39679C12.5176 5.24216 12.5297 5.22998 12.6877 5.22998C13.6285 5.22998 14.5694 5.22998 15.5103 5.22998L15.5096 5.22937Z"
        fill="white"
      />
    </svg>
  );
};

export default Eusd;
