import React from "react";

import { twMerge } from "tailwind-merge";

import { IconSvgProps } from "@/types/IconSvgProps";

const Cny = (props: IconSvgProps) => {
  const { className = "" } = props;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={twMerge(className)}
    >
      <path
        d="M12 24C5.37258 24 -2.89694e-07 18.6274 0 12C2.89694e-07 5.37258 5.37258 -2.89694e-07 12 0C18.6274 2.89694e-07 24 5.37258 24 12C24 18.6274 18.6274 24 12 24Z"
        fill="#D90026"
      />
      <path
        d="M13.7771 7.49897L12.5976 7.37195L13.4686 6.67029L13.2557 5.6928L14.1485 6.26623L15.0195 5.56456L14.8925 6.74408L15.6365 7.22193L14.659 7.43485L14.3832 8.5188L13.7771 7.49897Z"
        fill="#FFDB44"
      />
      <path
        d="M6.6 14.1375L3.675 15.6L4.65 12.675L2.7 10.725H5.625L6.6 7.8L8.0625 10.725H10.5L8.55 12.675L9.525 15.6L6.6 14.1375Z"
        fill="#FFDB44"
      />
      <path
        d="M13.7771 16.511L12.5976 16.638L13.4686 17.3397L13.2557 18.3171L14.1485 17.7437L15.0195 18.4454L14.8925 17.2659L15.6365 16.788L14.659 16.5751L14.3832 15.4911L13.7771 16.511Z"
        fill="#FFDB44"
      />
      <path
        d="M16.6242 10.6712L15.5304 10.2118L16.5659 9.78885L16.6416 8.79132L17.333 9.5963L18.3684 9.17331L17.909 10.2671L18.4851 10.9379L17.4876 10.8622L16.9131 11.8218L16.6242 10.6712Z"
        fill="#FFDB44"
      />
      <path
        d="M16.6242 14.019L15.5304 14.4783L16.5659 14.9013L16.6416 15.8988L17.333 15.0938L18.3684 15.5168L17.909 14.423L18.4851 13.7522L17.4876 13.828L16.9131 12.8683L16.6242 14.019Z"
        fill="#FFDB44"
      />
    </svg>
  );
};

export default Cny;
