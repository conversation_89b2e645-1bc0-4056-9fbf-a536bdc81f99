import React from "react";

import { twMerge } from "tailwind-merge";

import { IconSvgProps } from "@/types/IconSvgProps";

const EpochLetter = (props: IconSvgProps) => {
  const { className = "" } = props;

  return (
    <svg
      width="81"
      height="16"
      viewBox="0 0 81 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={twMerge(className)}
    >
      <g clipPath="url(#clip0_1456_13437)">
        <path
          d="M78.196 1.4926L78.196 14.7078C78.196 15.314 78.6948 15.8054 79.31 15.8054L79.8893 15.8054C80.5046 15.8054 81.0033 15.314 81.0033 14.7078L81.0033 1.4926C81.0033 0.886411 80.5046 0.394995 79.8893 0.394995L79.31 0.394995C78.6948 0.394995 78.196 0.886411 78.196 1.4926Z"
          fill="#353535"
        />
        <path
          d="M12.6234 13.4439C12.878 13.6948 13.0339 14.0397 13.0339 14.4223C13.0339 15.1875 12.4069 15.8053 11.6303 15.8053H1.40705C0.630437 15.8053 0.00341797 15.1875 0.00341797 14.4223V7.90252C0.00341797 7.51992 0.159377 7.17496 0.414004 6.92408C0.668631 6.6732 1.01874 6.51953 1.40705 6.51953H11.6335C12.0218 6.51953 12.3719 6.6732 12.6265 6.92408C12.8812 7.17496 13.0371 7.51992 13.0371 7.90252C13.0371 8.66771 12.4101 9.28551 11.6335 9.28551H2.81068V13.0393H11.6335C12.0218 13.0393 12.3719 13.193 12.6265 13.4439H12.6234Z"
          fill="#353535"
        />
        <path
          d="M11.9231 0.395142H1.11741C0.50217 0.395142 0.00341797 0.886558 0.00341797 1.49275V2.06351C0.00341797 2.6697 0.50217 3.16112 1.11741 3.16112H11.9231C12.5384 3.16112 13.0371 2.6697 13.0371 2.06351V1.49275C13.0371 0.886558 12.5384 0.395142 11.9231 0.395142Z"
          fill="#353535"
        />
        <path
          d="M24.2625 0.592651C22.2541 0.592651 20.5099 1.71535 19.6378 3.35863H24.2625C25.5897 3.35863 26.6687 4.42174 26.6687 5.72946C26.6687 7.03718 25.5897 8.1003 24.2625 8.1003H19.6378C20.5067 9.74044 22.2541 10.8663 24.2625 10.8663C27.1366 10.8663 29.476 8.56129 29.476 5.72946C29.476 2.89763 27.1398 0.592651 24.2625 0.592651Z"
          fill="#353535"
        />
        <path
          d="M25.666 1.97564C25.666 2.74083 25.039 3.35863 24.2624 3.35863H16.6426C16.2543 3.35863 15.9042 3.20496 15.6496 2.95408C15.395 2.7032 15.239 2.35823 15.239 1.97564C15.239 1.21045 15.866 0.592651 16.6426 0.592651H24.2624C24.6507 0.592651 25.0008 0.746317 25.2554 0.997199C25.51 1.24808 25.666 1.59304 25.666 1.97564Z"
          fill="#353535"
        />
        <path
          d="M25.666 9.48333C25.666 10.2485 25.039 10.8663 24.2624 10.8663H18.0463V14.4226C18.0463 14.8052 17.8903 15.1501 17.6357 15.401C17.3811 15.6519 17.031 15.8056 16.6426 15.8056C15.866 15.8056 15.239 15.1878 15.239 14.4226V9.48333C15.239 9.10073 15.395 8.75577 15.6496 8.50489C15.9042 8.25401 16.2543 8.10034 16.6426 8.10034H24.2624C24.6507 8.10034 25.0008 8.25401 25.2554 8.50489C25.51 8.75577 25.666 9.10073 25.666 9.48333Z"
          fill="#353535"
        />
        <path
          d="M64.5642 1.38299C64.5642 2.14818 63.9372 2.76597 63.1606 2.76597H56.744C56.3557 2.76597 56.0055 2.61231 55.7509 2.36143C55.4963 2.11054 55.3403 1.76558 55.3403 1.38299C55.3403 1.09761 55.4263 0.834183 55.5759 0.614661C55.8178 0.26029 56.222 0.0188161 56.6835 0.00313602C56.7026 0 56.7217 0 56.7408 0H63.1574C63.5457 0 63.8958 0.153665 64.1504 0.404547C64.405 0.655429 64.561 1.00039 64.561 1.38299H64.5642Z"
          fill="#353535"
        />
        <path
          d="M66.1681 14.4224L66.1681 1.77798C66.1681 1.39538 66.3241 1.05042 66.5787 0.799537C66.8333 0.548655 67.1834 0.394989 67.5718 0.394989C68.3484 0.394989 68.9754 1.01279 68.9754 1.77798L68.9754 7.11236L75.1915 7.11236C75.5798 7.11236 75.9299 7.26602 76.1845 7.5169C76.4391 7.76779 76.5951 8.11275 76.5951 8.49535C76.5951 9.26054 75.9681 9.87833 75.1915 9.87833L68.9754 9.87833L68.9754 14.4224C68.9754 14.805 68.8194 15.15 68.5648 15.4009C68.3102 15.6518 67.9601 15.8054 67.5718 15.8054C66.7951 15.8054 66.1681 15.1876 66.1681 14.4224Z"
          fill="#353535"
        />
        <path
          d="M64.5639 14.6204C64.5639 15.3856 63.9369 16.0034 63.1603 16.0034H56.7437C56.7246 16.0034 56.7055 16.0034 56.6864 16.0003C56.4509 15.994 56.2345 15.9281 56.0435 15.8184C55.9385 15.7588 55.8398 15.6835 55.7539 15.5989C55.4992 15.348 55.3433 15.003 55.3433 14.6204C55.3433 13.8552 55.9703 13.2374 56.7469 13.2374H63.1635C63.5518 13.2374 63.9019 13.3911 64.1565 13.642C64.4112 13.8929 64.5671 14.2378 64.5671 14.6204H64.5639Z"
          fill="#353535"
        />
        <path
          d="M56.7436 13.2278C56.6449 13.234 56.543 13.2372 56.4444 13.2372C55.5404 13.2372 54.6874 13.0114 53.9395 12.6162C52.2685 11.7319 51.1322 9.99451 51.1322 8C51.1322 6.00549 52.2685 4.26813 53.9395 3.38377C54.6874 2.98863 55.5404 2.76284 56.4444 2.76284C56.543 2.76284 56.6449 2.76284 56.7436 2.77225V2.76284H62.5777C61.139 1.12897 59.0352 0.0721286 56.6863 0.00313603C56.6067 0.00313603 56.5271 0 56.4444 0C51.9661 0 48.325 3.59075 48.325 8C48.325 11.7883 51.0081 14.9682 54.6015 15.793C54.9452 15.8714 55.3017 15.9279 55.6614 15.9624C55.9192 15.9875 56.1802 16 56.4444 16C56.5271 16 56.6067 16 56.6863 15.9969C59.0352 15.931 61.139 14.8742 62.5777 13.234H56.7436V13.2246V13.2278Z"
          fill="#353535"
        />
        <path
          d="M38.5979 16C34.1197 16 30.4785 12.4124 30.4785 8C30.4785 3.58761 34.1197 0 38.5979 0C43.0762 0 46.7173 3.58761 46.7173 8C46.7173 12.4124 43.0762 16 38.5979 16ZM38.5979 2.76284C35.6697 2.76284 33.2858 5.11172 33.2858 7.99686C33.2858 10.882 35.6697 13.2309 38.5979 13.2309C41.5261 13.2309 43.9101 10.882 43.9101 7.99686C43.9101 5.11172 41.5261 2.76284 38.5979 2.76284Z"
          fill="#353535"
        />
      </g>
      <defs>
        <clipPath id="clip0_1456_13437">
          <rect
            width="81"
            height="16"
            fill="white"
            transform="translate(0.00341797)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default EpochLetter;
