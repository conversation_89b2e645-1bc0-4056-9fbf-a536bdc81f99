import React from "react";

import { twMerge } from "tailwind-merge";

import { IconSvgProps } from "@/types/IconSvgProps";

const Usdt = (props: IconSvgProps) => {
  const { className = "" } = props;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={twMerge(className)}
    >
      <g clipPath="url(#clip0_7600_1543)">
        <path
          d="M24 12C24 18.6276 18.6276 24 12 24C5.37264 24 0 18.6276 0 12C0 5.37252 5.37264 0 12 0C18.6276 0 24 5.37252 24 12Z"
          fill="#1BA27A"
        />
        <path
          d="M17.6422 6.07648H6.33596V8.80603H10.6243V12.818H13.3539V8.80603H17.6422V6.07648Z"
          fill="white"
        />
        <path
          d="M12.0152 13.2453C8.46778 13.2453 5.59172 12.6839 5.59172 11.9913C5.59172 11.2987 8.46766 10.7372 12.0152 10.7372C15.5627 10.7372 18.4386 11.2987 18.4386 11.9913C18.4386 12.6839 15.5627 13.2453 12.0152 13.2453ZM19.2278 12.2003C19.2278 11.3072 15.9986 10.5833 12.0152 10.5833C8.03195 10.5833 4.80258 11.3072 4.80258 12.2003C4.80258 12.9868 7.30658 13.6421 10.6247 13.7873V19.5466H13.354V13.7896C16.6976 13.6489 19.2278 12.9909 19.2278 12.2003Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_7600_1543">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Usdt;
