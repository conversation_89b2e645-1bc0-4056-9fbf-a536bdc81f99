import React from "react";

import { twMerge } from "tailwind-merge";

import { IconSvgProps } from "@/types/IconSvgProps";

const Usdc = (props: IconSvgProps) => {
  const { className = "" } = props;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={twMerge(className)}
    >
      <path
        d="M12 24C18.65 24 24 18.65 24 12C24 5.34996 18.65 0 12 0C5.34996 0 0 5.34996 0 12C0 18.65 5.34996 24 12 24Z"
        fill="#2775CA"
      />
      <path
        d="M15.3 13.9C15.3 12.15 14.25 11.55 12.15 11.3001C10.65 11.1 10.35 10.7001 10.35 9.99998C10.35 9.2999 10.85 8.85002 11.85 8.85002C12.75 8.85002 13.25 9.15002 13.5 9.90002C13.55 10.05 13.7 10.15 13.85 10.15H14.65C14.85 10.15 15 9.99998 15 9.80006V9.75002C14.8 8.64998 13.9 7.80002 12.75 7.70006V6.50006C12.75 6.30002 12.6 6.15002 12.35 6.09998H11.6C11.4 6.09998 11.25 6.24998 11.2 6.50006V7.65002C9.69997 7.85006 8.75005 8.85002 8.75005 10.1001C8.75005 11.7501 9.75001 12.4 11.85 12.6501C13.25 12.9 13.7 13.2 13.7 14.0001C13.7 14.8001 13 15.3501 12.05 15.3501C10.75 15.3501 10.3 14.8 10.15 14.05C10.1 13.8501 9.95005 13.75 9.80005 13.75H8.94997C8.75005 13.75 8.60005 13.9 8.60005 14.1V14.1501C8.79997 15.4 9.60001 16.3 11.25 16.5501V17.7501C11.25 17.95 11.4 18.1 11.65 18.15H12.4C12.6 18.15 12.75 18 12.8 17.7501V16.5501C14.3 16.3 15.3 15.25 15.3 13.9Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.00004 9.54998C3.54996 13.4001 5.55 17.7501 9.45 19.15C9.6 19.2501 9.75 19.45 9.75 19.6V20.3001C9.75 20.4 9.75 20.4501 9.69996 20.5C9.65004 20.7 9.45 20.8 9.24996 20.7C6.45 19.8 4.29996 17.65 3.39996 14.85C1.89996 10.1001 4.5 5.04998 9.24996 3.54998C9.3 3.50006 9.39996 3.50006 9.45 3.50006C9.65004 3.54998 9.75 3.69998 9.75 3.90002V4.59998C9.75 4.85006 9.65004 5.00006 9.45 5.10002C7.40004 5.85002 5.75004 7.44998 5.00004 9.54998ZM14.3 3.74999C14.35 3.54995 14.55 3.44999 14.75 3.54995C17.5 4.44995 19.7 6.59999 20.6 9.45C22.1 14.2 19.5 19.25 14.75 20.75C14.7 20.8 14.6 20.8 14.55 20.8C14.35 20.75 14.25 20.6 14.25 20.4V19.7C14.25 19.45 14.35 19.3 14.55 19.2C16.6 18.45 18.25 16.85 19 14.75C20.45 10.9 18.45 6.54996 14.55 5.15003C14.4 5.04995 14.25 4.85003 14.25 4.64999V3.95003C14.25 3.84995 14.25 3.80003 14.3 3.74999Z"
        fill="white"
      />
    </svg>
  );
};

export default Usdc;
