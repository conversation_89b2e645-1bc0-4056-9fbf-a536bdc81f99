@theme {
  /* FONT FAMILY */
  --font-body:
    var(--font-pingfang, "PingFang SC"), "PingFang SC", -apple-system,
    sans-serif;
  --font-display:
    var(--font-pingfang, "PingFang SC"), "PingFang SC", -apple-system,
    sans-serif;
  --font-mono:
    var(--font-pingfang, "PingFang SC"), "SF Mono", "PingFang SC", ui-monospace,
    monospace;

  /* FONT WEIGHT */
  --font-weight-ultralight: 100;
  --font-weight-thin: 200;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;

  /* FONT SIZE */
  --text-xs: calc(var(--spacing) * 3);
  --text-xs--line-height: calc(var(--spacing) * 4.5);

  --text-sm: calc(var(--spacing) * 3.5);
  --text-sm--line-height: calc(var(--spacing) * 5);

  --text-md: calc(var(--spacing) * 4);
  --text-md--line-height: calc(var(--spacing) * 6);

  --text-lg: calc(var(--spacing) * 4.5);
  --text-lg--line-height: calc(var(--spacing) * 7);

  --text-xl: calc(var(--spacing) * 5);
  --text-xl--line-height: calc(var(--spacing) * 7.5);

  --text-display-xs: calc(var(--spacing) * 6);
  --text-display-xs--line-height: calc(var(--spacing) * 8);

  --text-display-sm: calc(var(--spacing) * 7.5);
  --text-display-sm--line-height: calc(var(--spacing) * 9.5);

  --text-display-md: calc(var(--spacing) * 9);
  --text-display-md--line-height: calc(var(--spacing) * 11);
  --text-display-md--letter-spacing: -0.72px;

  --text-display-lg: calc(var(--spacing) * 12);
  --text-display-lg--line-height: calc(var(--spacing) * 15);
  --text-display-lg--letter-spacing: -0.96px;

  --text-display-xl: calc(var(--spacing) * 15);
  --text-display-xl--line-height: calc(var(--spacing) * 18);
  --text-display-xl--letter-spacing: -1.2px;

  --text-display-2xl: calc(var(--spacing) * 18);
  --text-display-2xl--line-height: calc(var(--spacing) * 22.5);
  --text-display-2xl--letter-spacing: -1.44px;

  /* MAX WIDTH */
  --max-width-container: 1280px;

  /* BREAKPOINTS */
  --breakpoint-xxs: 320px;
  /* This must match the breakpoint in Sonner: https://github.com/emilkowalski/sonner/blob/main/src/styles.css */
  --breakpoint-xs: 600px;

  /* RADIUS */
  --radius-none: 0px;
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-DEFAULT: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* SHADOW */
  --shadow-xs: 0px 1px 2px rgba(10, 13, 18, 0.05);
  --shadow-sm:
    0px 1px 3px rgba(10, 13, 18, 0.1), 0px 1px 2px -1px rgba(10, 13, 18, 0.1);
  --shadow-md:
    0px 4px 6px -1px rgba(10, 13, 18, 0.1),
    0px 2px 4px -2px rgba(10, 13, 18, 0.06);
  --shadow-lg:
    0px 12px 16px -4px rgba(10, 13, 18, 0.08),
    0px 4px 6px -2px rgba(10, 13, 18, 0.03),
    0px 2px 2px -1px rgba(10, 13, 18, 0.04);
  --shadow-xl:
    0px 20px 24px -4px rgba(10, 13, 18, 0.08),
    0px 8px 8px -4px rgba(10, 13, 18, 0.03),
    0px 3px 3px -1.5px rgba(10, 13, 18, 0.04);
  --shadow-2xl:
    0px 24px 48px -12px rgba(10, 13, 18, 0.18),
    0px 4px 4px -2px rgba(10, 13, 18, 0.04);
  --shadow-3xl:
    0px 32px 64px -12px rgba(10, 13, 18, 0.14),
    0px 5px 5px -2.5px rgba(10, 13, 18, 0.04);

  --shadow-skeumorphic:
    0px 0px 0px 1px rgba(10, 13, 18, 0.18) inset,
    0px -2px 0px 0px rgba(10, 13, 18, 0.05) inset;
  --shadow-xs-skeumorphic: var(--shadow-skeumorphic), var(--shadow-xs);

  --shadow-modern-mockup-inner-lg:
    0px 0px 3.765px 1.255px rgba(10, 13, 18, 0.08) inset,
    0px 0px 2.51px 1.255px rgba(10, 13, 18, 0.03) inset;
  --shadow-modern-mockup-inner-md:
    0px 0px 1.692px 0.564px rgba(10, 13, 18, 0.08) inset,
    0px 0px 1.128px 0.564px rgba(10, 13, 18, 0.03) inset;
  --shadow-modern-mockup-inner-sm:
    0px 0px 4.48px 1.493px rgba(10, 13, 18, 0.08) inset,
    0px 0px 2.987px 1.493px rgba(10, 13, 18, 0.03) inset;

  --shadow-modern-mockup-outer-lg:
    0px 7.529px 10.039px -2.51px rgba(10, 13, 18, 0.08),
    0px 2.51px 3.765px -1.255px rgba(10, 13, 18, 0.03),
    0px 1.255px 1.255px -0.627px rgba(10, 13, 18, 0.04);
  --shadow-modern-mockup-outer-md:
    0px 3.385px 4.513px -1.128px rgba(10, 13, 18, 0.08),
    0px 1.128px 1.692px -0.564px rgba(10, 13, 18, 0.03),
    0px 0.564px 0.564px -0.282px rgba(10, 13, 18, 0.04);

  --drop-shadow-iphone-mockup: 20px 12px 18px rgba(16, 24, 40, 0.2);

  /* ANIMATIONS */
  --animate-marquee: marquee 60s linear infinite;
  --animate-caret-blink: caret-blink 1s infinite;

  @keyframes marquee {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  @keyframes caret-blink {
    0%,
    50% {
      opacity: 1;
    }
    51%,
    100% {
      opacity: 0;
    }
  }

  /* BASE COLOR */
  --color-transparent: 255 rgb(255 255 0);

  --color-white: rgb(255 255 255);
  --color-black: rgb(0 0 0);

  /* These will be inverted in dark mode. */
  --color-alpha-white: rgb(255 255 255);
  --color-alpha-black: rgb(0 0 0);

  --color-brand-25: #fafaff;
  --color-brand-50: #f4f3ff;
  --color-brand-100: #ebe9fe;
  --color-brand-200: #d9d6fe;
  --color-brand-300: #bdb4fe;
  --color-brand-400: #9b8afb;
  --color-brand-500: #7a5af8;
  --color-brand-600: #6938ef;
  --color-brand-700: #5925dc;
  --color-brand-800: #4a1fb8;
  --color-brand-900: #3e1c96;
  --color-brand-950: #27115f;

  --color-error-25: rgb(255 251 250);
  --color-error-50: rgb(254 243 242);
  --color-error-100: rgb(254 228 226);
  --color-error-200: rgb(254 205 202);
  --color-error-300: rgb(253 162 155);
  --color-error-400: rgb(249 112 102);
  --color-error-500: rgb(240 68 56);
  --color-error-600: rgb(217 45 32);
  --color-error-700: rgb(180 35 24);
  --color-error-800: rgb(145 32 24);
  --color-error-900: rgb(122 39 26);
  --color-error-950: rgb(85 22 12);

  --color-warning-25: rgb(255 252 245);
  --color-warning-50: rgb(255 250 235);
  --color-warning-100: rgb(254 240 199);
  --color-warning-200: rgb(254 223 137);
  --color-warning-300: rgb(254 200 75);
  --color-warning-400: rgb(253 176 34);
  --color-warning-500: rgb(247 144 9);
  --color-warning-600: rgb(220 104 3);
  --color-warning-700: rgb(181 71 8);
  --color-warning-800: rgb(147 55 13);
  --color-warning-900: rgb(122 46 14);
  --color-warning-950: rgb(78 29 9);

  --color-success-25: rgb(246 254 249);
  --color-success-50: rgb(236 253 243);
  --color-success-100: rgb(220 250 230);
  --color-success-200: rgb(171 239 198);
  --color-success-300: rgb(117 224 167);
  --color-success-400: rgb(71 205 137);
  --color-success-500: rgb(23 178 106);
  --color-success-600: rgb(7 148 85);
  --color-success-700: rgb(6 118 71);
  --color-success-800: rgb(8 93 58);
  --color-success-900: rgb(7 77 49);
  --color-success-950: rgb(5 51 33);

  --color-gray-25: rgb(253 253 253);
  --color-gray-50: rgb(250 250 250);
  --color-gray-100: rgb(245 245 245);
  --color-gray-200: rgb(233 234 235);
  --color-gray-300: rgb(213 215 218);
  --color-gray-400: rgb(164 167 174);
  --color-gray-500: rgb(113 118 128);
  --color-gray-600: rgb(83 88 98);
  --color-gray-700: rgb(65 70 81);
  --color-gray-800: rgb(37 43 55);
  --color-gray-900: rgb(24 29 39);
  --color-gray-950: rgb(10 13 18);

  --color-gray-blue-25: rgb(252 252 253);
  --color-gray-blue-50: rgb(248 249 252);
  --color-gray-blue-100: rgb(234 236 245);
  --color-gray-blue-200: rgb(213 217 235);
  --color-gray-blue-300: rgb(179 184 219);
  --color-gray-blue-400: rgb(113 123 188);
  --color-gray-blue-500: rgb(78 91 166);
  --color-gray-blue-600: rgb(62 71 132);
  --color-gray-blue-700: rgb(54 63 114);
  --color-gray-blue-800: rgb(41 48 86);
  --color-gray-blue-900: rgb(16 19 35);
  --color-gray-blue-950: rgb(13 15 28);

  --color-gray-cool-25: rgb(252 252 253);
  --color-gray-cool-50: rgb(249 249 251);
  --color-gray-cool-100: rgb(239 241 245);
  --color-gray-cool-200: rgb(220 223 234);
  --color-gray-cool-300: rgb(185 192 212);
  --color-gray-cool-400: rgb(125 137 176);
  --color-gray-cool-500: rgb(93 107 152);
  --color-gray-cool-600: rgb(74 85 120);
  --color-gray-cool-700: rgb(64 73 104);
  --color-gray-cool-800: rgb(48 55 79);
  --color-gray-cool-900: rgb(17 19 34);
  --color-gray-cool-950: rgb(14 16 27);

  --color-gray-modern-25: rgb(252 252 253);
  --color-gray-modern-50: rgb(248 250 252);
  --color-gray-modern-100: rgb(238 242 246);
  --color-gray-modern-200: rgb(227 232 239);
  --color-gray-modern-300: rgb(205 213 223);
  --color-gray-modern-400: rgb(154 164 178);
  --color-gray-modern-500: rgb(105 117 134);
  --color-gray-modern-600: rgb(75 85 101);
  --color-gray-modern-700: rgb(54 65 82);
  --color-gray-modern-800: rgb(32 41 57);
  --color-gray-modern-900: rgb(18 25 38);
  --color-gray-modern-950: rgb(13 18 28);

  --color-gray-neutral-25: rgb(252 252 253);
  --color-gray-neutral-50: rgb(249 250 251);
  --color-gray-neutral-100: rgb(243 244 246);
  --color-gray-neutral-200: rgb(229 231 235);
  --color-gray-neutral-300: rgb(210 214 219);
  --color-gray-neutral-400: rgb(157 164 174);
  --color-gray-neutral-500: rgb(108 115 127);
  --color-gray-neutral-600: rgb(77 87 97);
  --color-gray-neutral-700: rgb(56 66 80);
  --color-gray-neutral-800: rgb(31 42 55);
  --color-gray-neutral-900: rgb(17 25 39);
  --color-gray-neutral-950: rgb(13 18 28);

  --color-gray-iron-25: rgb(252 252 252);
  --color-gray-iron-50: rgb(250 250 250);
  --color-gray-iron-100: rgb(244 244 245);
  --color-gray-iron-200: rgb(228 228 231);
  --color-gray-iron-300: rgb(209 209 214);
  --color-gray-iron-400: rgb(160 160 171);
  --color-gray-iron-500: rgb(112 112 123);
  --color-gray-iron-600: rgb(81 82 92);
  --color-gray-iron-700: rgb(63 63 70);
  --color-gray-iron-800: rgb(38 39 43);
  --color-gray-iron-900: rgb(26 26 30);
  --color-gray-iron-950: rgb(19 19 22);

  --color-gray-true-25: rgb(252 252 252);
  --color-gray-true-50: rgb(247 247 247);
  --color-gray-true-100: rgb(245 245 245);
  --color-gray-true-200: rgb(229 229 229);
  --color-gray-true-300: rgb(214 214 214);
  --color-gray-true-400: rgb(163 163 163);
  --color-gray-true-500: rgb(115 115 115);
  --color-gray-true-600: rgb(82 82 82);
  --color-gray-true-700: rgb(66 66 66);
  --color-gray-true-800: rgb(41 41 41);
  --color-gray-true-900: rgb(20 20 20);
  --color-gray-true-950: rgb(15 15 15);

  --color-gray-warm-25: rgb(253 253 252);
  --color-gray-warm-50: rgb(250 250 249);
  --color-gray-warm-100: rgb(245 245 244);
  --color-gray-warm-200: rgb(231 229 228);
  --color-gray-warm-300: rgb(215 211 208);
  --color-gray-warm-400: rgb(169 162 157);
  --color-gray-warm-500: rgb(121 113 107);
  --color-gray-warm-600: rgb(87 83 78);
  --color-gray-warm-700: rgb(68 64 60);
  --color-gray-warm-800: rgb(41 37 36);
  --color-gray-warm-900: rgb(28 25 23);
  --color-gray-warm-950: rgb(23 20 18);

  --color-moss-25: rgb(250 253 247);
  --color-moss-50: rgb(245 251 238);
  --color-moss-100: rgb(230 244 215);
  --color-moss-200: rgb(206 234 176);
  --color-moss-300: rgb(172 220 121);
  --color-moss-400: rgb(134 203 60);
  --color-moss-500: rgb(102 159 42);
  --color-moss-600: rgb(79 122 33);
  --color-moss-700: rgb(63 98 26);
  --color-moss-800: rgb(51 80 21);
  --color-moss-900: rgb(43 66 18);
  --color-moss-950: rgb(26 40 11);

  --color-green-light-25: rgb(250 254 245);
  --color-green-light-50: rgb(243 254 231);
  --color-green-light-100: rgb(227 251 204);
  --color-green-light-200: rgb(208 248 171);
  --color-green-light-300: rgb(166 239 103);
  --color-green-light-400: rgb(133 225 58);
  --color-green-light-500: rgb(102 198 28);
  --color-green-light-600: rgb(76 163 13);
  --color-green-light-700: rgb(59 124 15);
  --color-green-light-800: rgb(50 98 18);
  --color-green-light-900: rgb(43 83 20);
  --color-green-light-950: rgb(21 41 10);

  --color-green-25: rgb(246 254 249);
  --color-green-50: rgb(237 252 242);
  --color-green-100: rgb(211 248 223);
  --color-green-200: rgb(170 240 196);
  --color-green-300: rgb(115 226 163);
  --color-green-400: rgb(60 203 127);
  --color-green-500: rgb(22 179 100);
  --color-green-600: rgb(9 146 80);
  --color-green-700: rgb(8 116 67);
  --color-green-800: rgb(9 92 55);
  --color-green-900: rgb(8 76 46);
  --color-green-950: rgb(5 46 28);

  --color-teal-25: rgb(246 254 252);
  --color-teal-50: rgb(240 253 249);
  --color-teal-100: rgb(204 251 239);
  --color-teal-200: rgb(153 246 224);
  --color-teal-300: rgb(95 233 208);
  --color-teal-400: rgb(46 211 183);
  --color-teal-500: rgb(21 183 158);
  --color-teal-600: rgb(14 147 132);
  --color-teal-700: rgb(16 117 105);
  --color-teal-800: rgb(18 93 86);
  --color-teal-900: rgb(19 78 72);
  --color-teal-950: rgb(10 41 38);

  --color-cyan-25: rgb(245 254 255);
  --color-cyan-50: rgb(236 253 255);
  --color-cyan-100: rgb(207 249 254);
  --color-cyan-200: rgb(165 240 252);
  --color-cyan-300: rgb(103 227 249);
  --color-cyan-400: rgb(34 204 238);
  --color-cyan-500: rgb(6 174 212);
  --color-cyan-600: rgb(8 138 178);
  --color-cyan-700: rgb(14 112 144);
  --color-cyan-800: rgb(21 91 117);
  --color-cyan-900: rgb(22 76 99);
  --color-cyan-950: rgb(13 45 58);

  --color-blue-light-25: rgb(245 251 255);
  --color-blue-light-50: rgb(240 249 255);
  --color-blue-light-100: rgb(224 242 254);
  --color-blue-light-200: rgb(185 230 254);
  --color-blue-light-300: rgb(124 212 253);
  --color-blue-light-400: rgb(54 191 250);
  --color-blue-light-500: rgb(11 165 236);
  --color-blue-light-600: rgb(0 134 201);
  --color-blue-light-700: rgb(2 106 162);
  --color-blue-light-800: rgb(6 89 134);
  --color-blue-light-900: rgb(11 74 111);
  --color-blue-light-950: rgb(6 44 65);

  --color-blue-25: rgb(245 250 255);
  --color-blue-50: rgb(239 248 255);
  --color-blue-100: rgb(209 233 255);
  --color-blue-200: rgb(178 221 255);
  --color-blue-300: rgb(132 202 255);
  --color-blue-400: rgb(83 177 253);
  --color-blue-500: rgb(46 144 250);
  --color-blue-600: rgb(21 112 239);
  --color-blue-700: rgb(23 92 211);
  --color-blue-800: rgb(24 73 169);
  --color-blue-900: rgb(25 65 133);
  --color-blue-950: rgb(16 42 86);

  --color-blue-dark-25: rgb(245 248 255);
  --color-blue-dark-50: rgb(239 244 255);
  --color-blue-dark-100: rgb(209 224 255);
  --color-blue-dark-200: rgb(178 204 255);
  --color-blue-dark-300: rgb(132 173 255);
  --color-blue-dark-400: rgb(82 139 255);
  --color-blue-dark-500: rgb(41 112 255);
  --color-blue-dark-600: rgb(21 94 239);
  --color-blue-dark-700: rgb(0 78 235);
  --color-blue-dark-800: rgb(0 64 193);
  --color-blue-dark-900: rgb(0 53 158);
  --color-blue-dark-950: rgb(0 34 102);

  --color-indigo-25: rgb(245 248 255);
  --color-indigo-50: rgb(238 244 255);
  --color-indigo-100: rgb(224 234 255);
  --color-indigo-200: rgb(199 215 254);
  --color-indigo-300: rgb(164 188 253);
  --color-indigo-400: rgb(128 152 249);
  --color-indigo-500: rgb(97 114 243);
  --color-indigo-600: rgb(68 76 231);
  --color-indigo-700: rgb(53 56 205);
  --color-indigo-800: rgb(45 49 166);
  --color-indigo-900: rgb(45 50 130);
  --color-indigo-950: rgb(31 35 91);

  --color-violet-25: rgb(251 250 255);
  --color-violet-50: rgb(245 243 255);
  --color-violet-100: rgb(236 233 254);
  --color-violet-200: rgb(221 214 254);
  --color-violet-300: rgb(195 181 253);
  --color-violet-400: rgb(164 138 251);
  --color-violet-500: rgb(135 91 247);
  --color-violet-600: rgb(120 57 238);
  --color-violet-700: rgb(105 39 218);
  --color-violet-800: rgb(87 32 183);
  --color-violet-900: rgb(73 28 150);
  --color-violet-950: rgb(46 18 94);

  --color-purple-25: rgb(250 250 255);
  --color-purple-50: rgb(244 243 255);
  --color-purple-100: rgb(235 233 254);
  --color-purple-200: rgb(217 214 254);
  --color-purple-300: rgb(189 180 254);
  --color-purple-400: rgb(155 138 251);
  --color-purple-500: rgb(122 90 248);
  --color-purple-600: rgb(105 56 239);
  --color-purple-700: rgb(89 37 220);
  --color-purple-800: rgb(74 31 184);
  --color-purple-900: rgb(62 28 150);
  --color-purple-950: rgb(39 17 95);

  --color-fuchsia-25: rgb(254 250 255);
  --color-fuchsia-50: rgb(253 244 255);
  --color-fuchsia-100: rgb(251 232 255);
  --color-fuchsia-200: rgb(246 208 254);
  --color-fuchsia-300: rgb(238 170 253);
  --color-fuchsia-400: rgb(228 120 250);
  --color-fuchsia-500: rgb(212 68 241);
  --color-fuchsia-600: rgb(186 36 213);
  --color-fuchsia-700: rgb(159 26 177);
  --color-fuchsia-800: rgb(130 24 144);
  --color-fuchsia-900: rgb(111 24 119);
  --color-fuchsia-950: rgb(71 16 76);

  --color-pink-25: rgb(254 246 251);
  --color-pink-50: rgb(253 242 250);
  --color-pink-100: rgb(252 231 246);
  --color-pink-200: rgb(252 206 238);
  --color-pink-300: rgb(250 167 224);
  --color-pink-400: rgb(246 112 199);
  --color-pink-500: rgb(238 70 188);
  --color-pink-600: rgb(221 37 144);
  --color-pink-700: rgb(193 21 116);
  --color-pink-800: rgb(158 22 95);
  --color-pink-900: rgb(133 22 81);
  --color-pink-950: rgb(78 13 48);

  --color-rose-25: rgb(255 245 246);
  --color-rose-50: rgb(255 241 243);
  --color-rose-100: rgb(255 228 232);
  --color-rose-200: rgb(254 205 214);
  --color-rose-300: rgb(254 163 180);
  --color-rose-400: rgb(253 111 142);
  --color-rose-500: rgb(246 61 104);
  --color-rose-600: rgb(227 27 84);
  --color-rose-700: rgb(192 16 72);
  --color-rose-800: rgb(161 16 67);
  --color-rose-900: rgb(137 18 62);
  --color-rose-950: rgb(81 11 36);

  --color-orange-dark-25: rgb(255 249 245);
  --color-orange-dark-50: rgb(255 244 237);
  --color-orange-dark-100: rgb(255 230 213);
  --color-orange-dark-200: rgb(255 214 174);
  --color-orange-dark-300: rgb(255 156 102);
  --color-orange-dark-400: rgb(255 105 46);
  --color-orange-dark-500: rgb(255 68 5);
  --color-orange-dark-600: rgb(230 46 5);
  --color-orange-dark-700: rgb(188 27 6);
  --color-orange-dark-800: rgb(151 24 12);
  --color-orange-dark-900: rgb(119 26 13);
  --color-orange-dark-950: rgb(87 19 10);

  --color-orange-25: rgb(254 250 245);
  --color-orange-50: rgb(254 246 238);
  --color-orange-100: rgb(253 234 215);
  --color-orange-200: rgb(249 219 175);
  --color-orange-300: rgb(247 178 122);
  --color-orange-400: rgb(243 135 68);
  --color-orange-500: rgb(239 104 32);
  --color-orange-600: rgb(224 79 22);
  --color-orange-700: rgb(185 56 21);
  --color-orange-800: rgb(147 47 25);
  --color-orange-900: rgb(119 41 23);
  --color-orange-950: rgb(81 28 16);

  --color-yellow-25: rgb(254 253 240);
  --color-yellow-50: rgb(254 251 232);
  --color-yellow-100: rgb(254 247 195);
  --color-yellow-200: rgb(254 238 149);
  --color-yellow-300: rgb(253 226 114);
  --color-yellow-400: rgb(250 197 21);
  --color-yellow-500: rgb(234 170 8);
  --color-yellow-600: rgb(202 133 4);
  --color-yellow-700: rgb(161 92 7);
  --color-yellow-800: rgb(133 74 14);
  --color-yellow-900: rgb(113 59 18);
  --color-yellow-950: rgb(84 44 13);

  /* LIGHT MODE VARIABLES */

  --color-alpha-white: rgb(255 255 255);
  --color-alpha-black: rgb(0 0 0);

  /* UTILITY COLORS */
  --color-utility-blue-50: var(--color-blue-50);
  --color-utility-blue-100: var(--color-blue-100);
  --color-utility-blue-200: var(--color-blue-200);
  --color-utility-blue-300: var(--color-blue-300);
  --color-utility-blue-400: var(--color-blue-400);
  --color-utility-blue-500: var(--color-blue-500);
  --color-utility-blue-600: var(--color-blue-600);
  --color-utility-blue-700: var(--color-blue-700);

  --color-utility-brand-50: var(--color-brand-50);
  --color-utility-brand-50_alt: var(--color-brand-50);
  --color-utility-brand-100: var(--color-brand-100);
  --color-utility-brand-100_alt: var(--color-brand-100);
  --color-utility-brand-200: var(--color-brand-200);
  --color-utility-brand-200_alt: var(--color-brand-200);
  --color-utility-brand-300: var(--color-brand-300);
  --color-utility-brand-300_alt: var(--color-brand-300);
  --color-utility-brand-400: var(--color-brand-400);
  --color-utility-brand-400_alt: var(--color-brand-400);
  --color-utility-brand-500: var(--color-brand-500);
  --color-utility-brand-500_alt: var(--color-brand-500);
  --color-utility-brand-600: var(--color-brand-600);
  --color-utility-brand-600_alt: var(--color-brand-600);
  --color-utility-brand-700: var(--color-brand-700);
  --color-utility-brand-700_alt: var(--color-brand-700);
  --color-utility-brand-800: var(--color-brand-800);
  --color-utility-brand-800_alt: var(--color-brand-800);
  --color-utility-brand-900: var(--color-brand-900);
  --color-utility-brand-900_alt: var(--color-brand-900);

  --color-utility-gray-50: var(--color-gray-50);
  --color-utility-gray-100: var(--color-gray-100);
  --color-utility-gray-200: var(--color-gray-200);
  --color-utility-gray-300: var(--color-gray-300);
  --color-utility-gray-400: var(--color-gray-400);
  --color-utility-gray-500: var(--color-gray-500);
  --color-utility-gray-600: var(--color-gray-600);
  --color-utility-gray-700: var(--color-gray-700);
  --color-utility-gray-800: var(--color-gray-800);
  --color-utility-gray-900: var(--color-gray-900);

  --color-utility-error-50: var(--color-error-50);
  --color-utility-error-100: var(--color-error-100);
  --color-utility-error-200: var(--color-error-200);
  --color-utility-error-300: var(--color-error-300);
  --color-utility-error-400: var(--color-error-400);
  --color-utility-error-500: var(--color-error-500);
  --color-utility-error-600: var(--color-error-600);
  --color-utility-error-700: var(--color-error-700);

  --color-utility-warning-50: var(--color-warning-50);
  --color-utility-warning-100: var(--color-warning-100);
  --color-utility-warning-200: var(--color-warning-200);
  --color-utility-warning-300: var(--color-warning-300);
  --color-utility-warning-400: var(--color-warning-400);
  --color-utility-warning-500: var(--color-warning-500);
  --color-utility-warning-600: var(--color-warning-600);
  --color-utility-warning-700: var(--color-warning-700);

  --color-utility-success-50: var(--color-success-50);
  --color-utility-success-100: var(--color-success-100);
  --color-utility-success-200: var(--color-success-200);
  --color-utility-success-300: var(--color-success-300);
  --color-utility-success-400: var(--color-success-400);
  --color-utility-success-500: var(--color-success-500);
  --color-utility-success-600: var(--color-success-600);
  --color-utility-success-700: var(--color-success-700);

  --color-utility-orange-50: var(--color-orange-50);
  --color-utility-orange-100: var(--color-orange-100);
  --color-utility-orange-200: var(--color-orange-200);
  --color-utility-orange-300: var(--color-orange-300);
  --color-utility-orange-400: var(--color-orange-400);
  --color-utility-orange-500: var(--color-orange-500);
  --color-utility-orange-600: var(--color-orange-600);
  --color-utility-orange-700: var(--color-orange-700);

  --color-utility-blue-dark-50: var(--color-blue-dark-50);
  --color-utility-blue-dark-100: var(--color-blue-dark-100);
  --color-utility-blue-dark-200: var(--color-blue-dark-200);
  --color-utility-blue-dark-300: var(--color-blue-dark-300);
  --color-utility-blue-dark-400: var(--color-blue-dark-400);
  --color-utility-blue-dark-500: var(--color-blue-dark-500);
  --color-utility-blue-dark-600: var(--color-blue-dark-600);
  --color-utility-blue-dark-700: var(--color-blue-dark-700);

  --color-utility-indigo-50: var(--color-indigo-50);
  --color-utility-indigo-100: var(--color-indigo-100);
  --color-utility-indigo-200: var(--color-indigo-200);
  --color-utility-indigo-300: var(--color-indigo-300);
  --color-utility-indigo-400: var(--color-indigo-400);
  --color-utility-indigo-500: var(--color-indigo-500);
  --color-utility-indigo-600: var(--color-indigo-600);
  --color-utility-indigo-700: var(--color-indigo-700);

  --color-utility-fuchsia-50: var(--color-fuchsia-50);
  --color-utility-fuchsia-100: var(--color-fuchsia-100);
  --color-utility-fuchsia-200: var(--color-fuchsia-200);
  --color-utility-fuchsia-300: var(--color-fuchsia-300);
  --color-utility-fuchsia-400: var(--color-fuchsia-400);
  --color-utility-fuchsia-500: var(--color-fuchsia-500);
  --color-utility-fuchsia-600: var(--color-fuchsia-600);
  --color-utility-fuchsia-700: var(--color-fuchsia-700);

  --color-utility-pink-50: var(--color-pink-50);
  --color-utility-pink-100: var(--color-pink-100);
  --color-utility-pink-200: var(--color-pink-200);
  --color-utility-pink-300: var(--color-pink-300);
  --color-utility-pink-400: var(--color-pink-400);
  --color-utility-pink-500: var(--color-pink-500);
  --color-utility-pink-600: var(--color-pink-600);
  --color-utility-pink-700: var(--color-pink-700);

  --color-utility-purple-50: var(--color-purple-50);
  --color-utility-purple-100: var(--color-purple-100);
  --color-utility-purple-200: var(--color-purple-200);
  --color-utility-purple-300: var(--color-purple-300);
  --color-utility-purple-400: var(--color-purple-400);
  --color-utility-purple-500: var(--color-purple-500);
  --color-utility-purple-600: var(--color-purple-600);
  --color-utility-purple-700: var(--color-purple-700);

  --color-utility-orange-dark-50: var(--color-orange-dark-50);
  --color-utility-orange-dark-100: var(--color-orange-dark-100);
  --color-utility-orange-dark-200: var(--color-orange-dark-200);
  --color-utility-orange-dark-300: var(--color-orange-dark-300);
  --color-utility-orange-dark-400: var(--color-orange-dark-400);
  --color-utility-orange-dark-500: var(--color-orange-dark-500);
  --color-utility-orange-dark-600: var(--color-orange-dark-600);
  --color-utility-orange-dark-700: var(--color-orange-dark-700);

  --color-utility-blue-light-50: var(--color-blue-light-50);
  --color-utility-blue-light-100: var(--color-blue-light-100);
  --color-utility-blue-light-200: var(--color-blue-light-200);
  --color-utility-blue-light-300: var(--color-blue-light-300);
  --color-utility-blue-light-400: var(--color-blue-light-400);
  --color-utility-blue-light-500: var(--color-blue-light-500);
  --color-utility-blue-light-600: var(--color-blue-light-600);
  --color-utility-blue-light-700: var(--color-blue-light-700);

  --color-utility-gray-blue-50: var(--color-gray-blue-50);
  --color-utility-gray-blue-100: var(--color-gray-blue-100);
  --color-utility-gray-blue-200: var(--color-gray-blue-200);
  --color-utility-gray-blue-300: var(--color-gray-blue-300);
  --color-utility-gray-blue-400: var(--color-gray-blue-400);
  --color-utility-gray-blue-500: var(--color-gray-blue-500);
  --color-utility-gray-blue-600: var(--color-gray-blue-600);
  --color-utility-gray-blue-700: var(--color-gray-blue-700);

  --color-utility-green-50: var(--color-green-50);
  --color-utility-green-100: var(--color-green-100);
  --color-utility-green-200: var(--color-green-200);
  --color-utility-green-300: var(--color-green-300);
  --color-utility-green-400: var(--color-green-400);
  --color-utility-green-500: var(--color-green-500);
  --color-utility-green-600: var(--color-green-600);
  --color-utility-green-700: var(--color-green-700);

  --color-utility-yellow-50: var(--color-yellow-50);
  --color-utility-yellow-100: var(--color-yellow-100);
  --color-utility-yellow-200: var(--color-yellow-200);
  --color-utility-yellow-300: var(--color-yellow-300);
  --color-utility-yellow-400: var(--color-yellow-400);
  --color-utility-yellow-500: var(--color-yellow-500);
  --color-utility-yellow-600: var(--color-yellow-600);
  --color-utility-yellow-700: var(--color-yellow-700);

  /* TEXT COLORS */
  --color-text-primary: var(--color-gray-900);
  --color-text-tertiary: var(--color-gray-600);
  --color-text-error-primary: var(--color-error-600);
  --color-text-warning-primary: var(--color-warning-600);
  --color-text-success-primary: var(--color-success-600);
  --color-text-white: var(--color-white);
  --color-text-secondary: var(--color-gray-700);
  --color-text-disabled: var(--color-gray-500);
  --color-text-secondary_hover: var(--color-gray-800);
  --color-text-tertiary_hover: var(--color-gray-700);
  --color-text-brand-secondary: var(--color-brand-600);
  --color-text-placeholder: var(--color-gray-500);
  --color-text-placeholder_subtle: var(--color-gray-300);
  --color-text-brand-tertiary: var(--color-brand-500);
  --color-text-editor-icon-fg: var(--color-gray-400);
  --color-text-editor-icon-fg_active: var(--color-gray-500);
  --color-text-quaternary: var(--color-gray-500);
  --color-text-brand-primary: var(--color-brand-900);
  --color-text-primary_on-brand: var(--color-white);
  --color-text-secondary_on-brand: var(--color-brand-200);
  --color-text-tertiary_on-brand: var(--color-brand-200);
  --color-text-quaternary_on-brand: var(--color-brand-300);
  --color-text-brand-tertiary_alt: var(--color-brand-500);
  --color-text-brand-secondary_hover: var(--color-brand-700);
  --color-text-error-primary_hover: var(--color-error-700);

  /* BORDER COLORS */
  --color-border-secondary: var(--color-gray-200);
  --color-border-error_subtle: var(--color-error-300);
  --color-border-primary: var(--color-gray-300);
  --color-border-brand: var(--color-brand-400);
  --color-border-disabled: var(--color-gray-300);
  --color-border-error: var(--color-error-500);
  --color-border-disabled_subtle: var(--color-gray-200);
  --color-border-tertiary: var(--color-gray-100);
  --color-border-brand_alt: var(--color-brand-500);
  --color-border-secondary_alt: rgb(0 0 0 / 0.08);

  /* FOREGROUND COLORS */
  --color-fg-secondary: var(--color-gray-700);
  --color-fg-warning-primary: var(--color-warning-600);
  --color-fg-success-primary: var(--color-success-600);
  --color-fg-white: var(--color-white);
  --color-fg-success-secondary: var(--color-success-500);
  --color-fg-secondary_hover: var(--color-gray-800);
  --color-fg-primary: var(--color-gray-900);
  --color-fg-disabled: var(--color-gray-400);
  --color-fg-brand-secondary: var(--color-brand-500);
  --color-fg-brand-primary: var(--color-brand-500);
  --color-fg-quaternary: var(--color-gray-400);
  --color-fg-quaternary_hover: var(--color-gray-500);
  --color-fg-error-primary: var(--color-error-600);
  --color-fg-disabled_subtle: var(--color-gray-300);
  --color-fg-warning-secondary: var(--color-warning-500);
  --color-fg-error-secondary: var(--color-error-500);
  --color-fg-tertiary: var(--color-gray-600);
  --color-fg-tertiary_hover: var(--color-gray-700);
  --color-fg-brand-primary_alt: var(--color-fg-brand-primary);
  --color-fg-brand-secondary_alt: var(--color-fg-brand-secondary);
  --color-fg-brand-secondary_hover: var(--color-brand-600);

  /* BACKGROUND COLORS */
  --color-bg-primary: var(--color-white);
  --color-bg-tertiary: var(--color-gray-100);
  --color-bg-brand-primary: var(--color-brand-50);
  --color-bg-error-secondary: var(--color-error-100);
  --color-bg-warning-primary: var(--color-warning-50);
  --color-bg-warning-secondary: var(--color-warning-100);
  --color-bg-success-primary: var(--color-success-50);
  --color-bg-success-secondary: var(--color-success-100);
  --color-bg-brand-solid: var(--color-brand-500);
  --color-bg-secondary-solid: var(--color-gray-600);
  --color-bg-error-solid: var(--color-error-600);
  --color-bg-warning-solid: var(--color-warning-600);
  --color-bg-success-solid: var(--color-success-600);
  --color-bg-secondary_hover: var(--color-gray-100);
  --color-bg-primary_hover: var(--color-gray-50);
  --color-bg-disabled: var(--color-gray-100);
  --color-bg-active: var(--color-gray-50);
  --color-bg-brand-solid_hover: var(--color-brand-600);
  --color-bg-error-primary: var(--color-error-50);
  --color-bg-brand-secondary: var(--color-brand-100);
  --color-bg-secondary: var(--color-gray-50);
  --color-bg-disabled_subtle: var(--color-gray-50);
  --color-bg-quaternary: var(--color-gray-200);
  --color-bg-primary_alt: var(--color-white);
  --color-bg-brand-primary_alt: var(--color-brand-50);
  --color-bg-secondary_alt: var(--color-gray-50);
  --color-bg-overlay: var(--color-gray-950);
  --color-bg-secondary_subtle: var(--color-gray-25);
  --color-bg-brand-section: var(--color-brand-700);
  --color-bg-brand-section_subtle: var(--color-brand-600);
  --color-bg-primary-solid: var(--color-gray-950);

  /* COMPONENT COLORS */
  --color-app-store-badge-border: rgb(166 166 166);
  --color-avatar-bg: var(--color-gray-100);
  --color-avatar-contrast-border: rgb(0 0 0 / 0.08);
  --color-avatar-profile-photo-border: var(--color-white);
  --color-avatar-styles-bg-neutral: rgb(224 224 224);
  --color-button-destructive-primary-icon: var(--color-error-300);
  --color-button-destructive-primary-icon_hover: var(--color-error-200);
  --color-button-primary-icon: var(--color-brand-300);
  --color-button-primary-icon_hover: var(--color-brand-200);
  --color-featured-icon-light-fg-brand: var(--color-brand-600);
  --color-featured-icon-light-fg-error: var(--color-error-600);
  --color-featured-icon-light-fg-gray: var(--color-gray-500);
  --color-featured-icon-light-fg-success: var(--color-success-600);
  --color-featured-icon-light-fg-warning: var(--color-warning-600);
  --color-focus-ring-error: var(--color-error-500);
  --color-focus-ring: var(--color-brand-500);
  --color-footer-button-fg: var(--color-brand-200);
  --color-footer-button-fg_hover: var(--color-white);
  --color-icon-fg-brand: var(--color-brand-600);
  --color-icon-fg-brand_on-brand: var(--color-brand-200);
  --color-screen-mockup-border: var(--color-gray-900);
  --color-slider-handle-bg: var(--color-white);
  --color-slider-handle-border: var(--color-brand-600);
  --color-toggle-border: var(--color-gray-300);
  --color-toggle-button-fg_disabled: var(--color-gray-50);
  --color-toggle-slim-border_pressed-hover: var(--color-bg-brand-solid_hover);
  --color-toggle-slim-border_pressed: var(--color-bg-brand-solid);
  --color-tooltip-supporting-text: var(--color-gray-300);
  --color-text-editor-icon-fg: var(--color-gray-400);
  --color-text-editor-icon-fg_active: var(--color-gray-500);

  /* BACKGROUND PROPERTY COLORS */
  --background-color-quaternary: var(--color-bg-quaternary);
  --background-color-brand-solid: var(--color-bg-brand-solid);
  --background-color-disabled: var(--color-bg-disabled);
  --background-color-primary: var(--color-bg-primary);
  --background-color-primary-solid: var(--color-bg-primary-solid);
  --background-color-primary_alt: var(--color-bg-primary_alt);
  --background-color-primary_hover: var(--color-bg-primary_hover);
  --background-color-secondary: var(--color-bg-secondary);
  --background-color-secondary-solid: var(--color-bg-secondary-solid);
  --background-color-secondary_alt: var(--color-bg-secondary_alt);
  --background-color-secondary_hover: var(--color-bg-secondary_hover);
  --background-color-secondary_subtle: var(--color-bg-secondary_subtle);
  --background-color-tertiary: var(--color-bg-tertiary);
  --background-color-active: var(--color-bg-active);
  --background-color-disabled_subtle: var(--color-bg-disabled_subtle);
  --background-color-overlay: var(--color-bg-overlay);
  --background-color-brand-primary: var(--color-bg-brand-primary);
  --background-color-brand-primary_alt: var(--color-bg-brand-primary_alt);
  --background-color-brand-secondary: var(--color-bg-brand-secondary);
  --background-color-brand-solid: var(--color-bg-brand-solid);
  --background-color-brand-solid_hover: var(--color-bg-brand-solid_hover);
  --background-color-brand-section: var(--color-bg-brand-section);
  --background-color-brand-section_subtle: var(--color-bg-brand-section_subtle);
  --background-color-error-primary: var(--color-bg-error-primary);
  --background-color-error-secondary: var(--color-bg-error-secondary);
  --background-color-error-solid: var(--color-bg-error-solid);
  --background-color-warning-primary: var(--color-bg-warning-primary);
  --background-color-warning-secondary: var(--color-bg-warning-secondary);
  --background-color-warning-solid: var(--color-bg-warning-solid);
  --background-color-success-primary: var(--color-bg-success-primary);
  --background-color-success-secondary: var(--color-bg-success-secondary);
  --background-color-success-solid: var(--color-bg-success-solid);
  --background-color-border-brand: var(--color-border-brand);
  --background-color-border-tertiary: var(--color-border-tertiary);
  --background-color-border-brand_alt: var(--color-border-brand_alt);

  /* TEXT PROPERTY COLORS */
  --text-color-primary: var(--color-text-primary);
  --text-color-primary_on-brand: var(--color-text-primary_on-brand);
  --text-color-secondary: var(--color-text-secondary);
  --text-color-secondary_hover: var(--color-text-secondary_hover);
  --text-color-secondary_on-brand: var(--color-text-secondary_on-brand);
  --text-color-tertiary: var(--color-text-tertiary);
  --text-color-tertiary_hover: var(--color-text-tertiary_hover);
  --text-color-tertiary_on-brand: var(--color-text-tertiary_on-brand);
  --text-color-quaternary: var(--color-text-quaternary);
  --text-color-quaternary_on-brand: var(--color-text-quaternary_on-brand);
  --text-color-disabled: var(--color-text-disabled);
  --text-color-placeholder: var(--color-text-placeholder);
  --text-color-placeholder_subtle: var(--color-text-placeholder_subtle);
  --text-color-brand-primary: var(--color-text-brand-primary);
  --text-color-brand-secondary: var(--color-text-brand-secondary);
  --text-color-brand-secondary_hover: var(--color-text-brand-secondary_hover);
  --text-color-brand-tertiary: var(--color-text-brand-tertiary);
  --text-color-brand-tertiary_alt: var(--color-text-brand-tertiary_alt);
  --text-color-error-primary: var(--color-text-error-primary);
  --text-color-error-primary_hover: var(--color-text-error-primary_hover);
  --text-color-warning-primary: var(--color-text-warning-primary);
  --text-color-success-primary: var(--color-text-success-primary);
  --text-color-tooltip-supporting-text: var(--color-tooltip-supporting-text);

  /* BORDER PROPERTY COLORS */
  --border-color-primary: var(--color-border-primary);
  --border-color-secondary: var(--color-border-secondary);
  --border-color-secondary_alt: var(--color-border-secondary_alt);
  --border-color-tertiary: var(--color-border-tertiary);
  --border-color-disabled: var(--color-border-disabled);
  --border-color-brand: var(--color-border-brand);
  --border-color-brand-solid: var(--color-bg-brand-solid);
  --border-color-brand-solid_hover: var(--color-bg-brand-solid_hover);
  --border-color-error: var(--color-border-error);
  --border-color-disabled_subtle: var(--color-border-disabled_subtle);
  --border-color-brand_alt: var(--color-border-brand_alt);
  --border-color-error_subtle: var(--color-border-error_subtle);

  /* RING PROPERTY COLORS */
  --ring-color-bg-brand-solid: var(--color-bg-brand-solid);
  --ring-color-primary: var(--color-border-primary);
  --ring-color-secondary: var(--color-border-secondary);
  --ring-color-secondary_alt: var(--color-border-secondary_alt);
  --ring-color-tertiary: var(--color-border-tertiary);
  --ring-color-disabled: var(--color-border-disabled);
  --ring-color-brand: var(--color-border-brand);
  --ring-color-brand-solid: var(--color-bg-brand-solid);
  --ring-color-brand-solid_hover: var(--color-bg-brand-solid_hover);
  --ring-color-error: var(--color-border-error);
  --ring-color-disabled_subtle: var(--color-border-disabled_subtle);
  --ring-color-brand_alt: var(--color-border-brand_alt);
  --ring-color-error_subtle: var(--color-border-error_subtle);

  /* OUTLINE PROPERTY COLORS */
  --outline-color-brand: var(--color-border-brand);
  --outline-color-primary: var(--color-border-primary);
  --outline-color-secondary: var(--color-border-secondary);
  --outline-color-secondary_alt: var(--color-border-secondary_alt);
  --outline-color-tertiary: var(--color-border-tertiary);
  --outline-color-disabled: var(--color-border-disabled);
  --outline-color-brand: var(--color-border-brand);
  --outline-color-brand-solid: var(--color-bg-brand-solid);
  --outline-color-brand-solid_hover: var(--color-bg-brand-solid_hover);
  --outline-color-error: var(--color-border-error);
  --outline-color-disabled_subtle: var(--color-border-disabled_subtle);
  --outline-color-brand_alt: var(--color-border-brand_alt);
  --outline-color-error_subtle: var(--color-border-error_subtle);

  /* OPACITY PROPERTY COLORS */
  --background-color-brand-tertiary-04: rgba(122, 90, 248, 0.04);
}

@layer base {
  /* DARK MODE VARIABLES */

  .dark-mode {
    --color-alpha-white: rgb(12 14 18);
    --color-alpha-black: rgb(255 255 255);

    /* UTILITY COLORS */

    --color-gray-25: rgb(250 250 250);
    --color-gray-50: rgb(247 247 247);
    --color-gray-100: rgb(240 240 241);
    --color-gray-200: rgb(236 236 237);
    --color-gray-300: rgb(206 207 210);
    --color-gray-400: rgb(148 151 156);
    --color-gray-500: rgb(133 136 142);
    --color-gray-600: rgb(97 101 108);
    --color-gray-700: rgb(55 58 65);
    --color-gray-800: rgb(34 38 47);
    --color-gray-900: rgb(19 22 27);
    --color-gray-950: rgb(12 14 18);

    --color-utility-blue-50: var(--color-blue-950);
    --color-utility-blue-100: var(--color-blue-900);
    --color-utility-blue-200: var(--color-blue-800);
    --color-utility-blue-300: var(--color-blue-700);
    --color-utility-blue-400: var(--color-blue-600);
    --color-utility-blue-500: var(--color-blue-500);
    --color-utility-blue-600: var(--color-blue-400);
    --color-utility-blue-700: var(--color-blue-300);

    --color-utility-brand-50: var(--color-brand-950);
    --color-utility-brand-50_alt: var(--color-utility-gray-50);
    --color-utility-brand-100: var(--color-brand-900);
    --color-utility-brand-100_alt: var(--color-utility-gray-100);
    --color-utility-brand-200: var(--color-brand-800);
    --color-utility-brand-200_alt: var(--color-utility-gray-200);
    --color-utility-brand-300: var(--color-brand-700);
    --color-utility-brand-300_alt: var(--color-utility-gray-300);
    --color-utility-brand-400: var(--color-brand-600);
    --color-utility-brand-400_alt: var(--color-utility-gray-400);
    --color-utility-brand-500: var(--color-brand-500);
    --color-utility-brand-500_alt: var(--color-utility-gray-500);
    --color-utility-brand-600: var(--color-brand-400);
    --color-utility-brand-600_alt: var(--color-utility-gray-600);
    --color-utility-brand-700: var(--color-brand-300);
    --color-utility-brand-700_alt: var(--color-utility-gray-700);
    --color-utility-brand-800: var(--color-brand-200);
    --color-utility-brand-800_alt: var(--color-utility-gray-800);
    --color-utility-brand-900: var(--color-brand-100);
    --color-utility-brand-900_alt: var(--color-utility-gray-900);

    --color-utility-gray-50: var(--color-gray-900);
    --color-utility-gray-100: var(--color-gray-800);
    --color-utility-gray-200: var(--color-gray-700);
    --color-utility-gray-300: var(--color-gray-700);
    --color-utility-gray-400: var(--color-gray-600);
    --color-utility-gray-500: var(--color-gray-500);
    --color-utility-gray-600: var(--color-gray-400);
    --color-utility-gray-700: var(--color-gray-300);
    --color-utility-gray-800: var(--color-gray-200);
    --color-utility-gray-900: var(--color-gray-100);

    --color-utility-error-50: var(--color-error-950);
    --color-utility-error-100: var(--color-error-900);
    --color-utility-error-200: var(--color-error-800);
    --color-utility-error-300: var(--color-error-700);
    --color-utility-error-400: var(--color-error-600);
    --color-utility-error-500: var(--color-error-500);
    --color-utility-error-600: var(--color-error-400);
    --color-utility-error-700: var(--color-error-300);

    --color-utility-warning-50: var(--color-warning-950);
    --color-utility-warning-100: var(--color-warning-900);
    --color-utility-warning-200: var(--color-warning-800);
    --color-utility-warning-300: var(--color-warning-700);
    --color-utility-warning-400: var(--color-warning-600);
    --color-utility-warning-500: var(--color-warning-500);
    --color-utility-warning-600: var(--color-warning-400);
    --color-utility-warning-700: var(--color-warning-300);

    --color-utility-success-50: var(--color-success-950);
    --color-utility-success-100: var(--color-success-900);
    --color-utility-success-200: var(--color-success-800);
    --color-utility-success-300: var(--color-success-700);
    --color-utility-success-400: var(--color-success-600);
    --color-utility-success-500: var(--color-success-500);
    --color-utility-success-600: var(--color-success-400);
    --color-utility-success-700: var(--color-success-300);

    --color-utility-orange-50: var(--color-orange-950);
    --color-utility-orange-100: var(--color-orange-900);
    --color-utility-orange-200: var(--color-orange-800);
    --color-utility-orange-300: var(--color-orange-700);
    --color-utility-orange-400: var(--color-orange-600);
    --color-utility-orange-500: var(--color-orange-500);
    --color-utility-orange-600: var(--color-orange-400);
    --color-utility-orange-700: var(--color-orange-300);

    --color-utility-blue-dark-50: var(--color-blue-dark-950);
    --color-utility-blue-dark-100: var(--color-blue-dark-900);
    --color-utility-blue-dark-200: var(--color-blue-dark-800);
    --color-utility-blue-dark-300: var(--color-blue-dark-700);
    --color-utility-blue-dark-400: var(--color-blue-dark-600);
    --color-utility-blue-dark-500: var(--color-blue-dark-500);
    --color-utility-blue-dark-600: var(--color-blue-dark-400);
    --color-utility-blue-dark-700: var(--color-blue-dark-300);

    --color-utility-indigo-50: var(--color-indigo-950);
    --color-utility-indigo-100: var(--color-indigo-900);
    --color-utility-indigo-200: var(--color-indigo-800);
    --color-utility-indigo-300: var(--color-indigo-700);
    --color-utility-indigo-400: var(--color-indigo-600);
    --color-utility-indigo-500: var(--color-indigo-500);
    --color-utility-indigo-600: var(--color-indigo-400);
    --color-utility-indigo-700: var(--color-indigo-300);

    --color-utility-fuchsia-50: var(--color-fuchsia-950);
    --color-utility-fuchsia-100: var(--color-fuchsia-900);
    --color-utility-fuchsia-200: var(--color-fuchsia-800);
    --color-utility-fuchsia-300: var(--color-fuchsia-700);
    --color-utility-fuchsia-400: var(--color-fuchsia-600);
    --color-utility-fuchsia-500: var(--color-fuchsia-500);
    --color-utility-fuchsia-600: var(--color-fuchsia-400);
    --color-utility-fuchsia-700: var(--color-fuchsia-300);

    --color-utility-pink-50: var(--color-pink-950);
    --color-utility-pink-100: var(--color-pink-900);
    --color-utility-pink-200: var(--color-pink-800);
    --color-utility-pink-300: var(--color-pink-700);
    --color-utility-pink-400: var(--color-pink-600);
    --color-utility-pink-500: var(--color-pink-500);
    --color-utility-pink-600: var(--color-pink-400);
    --color-utility-pink-700: var(--color-pink-300);

    --color-utility-purple-50: var(--color-purple-950);
    --color-utility-purple-100: var(--color-purple-900);
    --color-utility-purple-200: var(--color-purple-800);
    --color-utility-purple-300: var(--color-purple-700);
    --color-utility-purple-400: var(--color-purple-600);
    --color-utility-purple-500: var(--color-purple-500);
    --color-utility-purple-600: var(--color-purple-400);
    --color-utility-purple-700: var(--color-purple-300);

    --color-utility-orange-dark-50: var(--color-orange-dark-950);
    --color-utility-orange-dark-100: var(--color-orange-dark-900);
    --color-utility-orange-dark-200: var(--color-orange-dark-800);
    --color-utility-orange-dark-300: var(--color-orange-dark-700);
    --color-utility-orange-dark-400: var(--color-orange-dark-600);
    --color-utility-orange-dark-500: var(--color-orange-dark-500);
    --color-utility-orange-dark-600: var(--color-orange-dark-400);
    --color-utility-orange-dark-700: var(--color-orange-dark-300);

    --color-utility-blue-light-50: var(--color-blue-light-950);
    --color-utility-blue-light-100: var(--color-blue-light-900);
    --color-utility-blue-light-200: var(--color-blue-light-800);
    --color-utility-blue-light-300: var(--color-blue-light-700);
    --color-utility-blue-light-400: var(--color-blue-light-600);
    --color-utility-blue-light-500: var(--color-blue-light-500);
    --color-utility-blue-light-600: var(--color-blue-light-400);
    --color-utility-blue-light-700: var(--color-blue-light-300);

    --color-utility-gray-blue-50: var(--color-gray-blue-950);
    --color-utility-gray-blue-100: var(--color-gray-blue-900);
    --color-utility-gray-blue-200: var(--color-gray-blue-800);
    --color-utility-gray-blue-300: var(--color-gray-blue-700);
    --color-utility-gray-blue-400: var(--color-gray-blue-600);
    --color-utility-gray-blue-500: var(--color-gray-blue-500);
    --color-utility-gray-blue-600: var(--color-gray-blue-400);
    --color-utility-gray-blue-700: var(--color-gray-blue-300);

    --color-utility-green-50: var(--color-green-950);
    --color-utility-green-100: var(--color-green-900);
    --color-utility-green-200: var(--color-green-800);
    --color-utility-green-300: var(--color-green-700);
    --color-utility-green-400: var(--color-green-600);
    --color-utility-green-500: var(--color-green-500);
    --color-utility-green-600: var(--color-green-400);
    --color-utility-green-700: var(--color-green-300);

    --color-utility-yellow-50: var(--color-yellow-950);
    --color-utility-yellow-100: var(--color-yellow-900);
    --color-utility-yellow-200: var(--color-yellow-800);
    --color-utility-yellow-300: var(--color-yellow-700);
    --color-utility-yellow-400: var(--color-yellow-600);
    --color-utility-yellow-500: var(--color-yellow-500);
    --color-utility-yellow-600: var(--color-yellow-400);
    --color-utility-yellow-700: var(--color-yellow-300);

    /* TEXT COLORS */

    --color-text-primary: var(--color-gray-50);
    --color-text-tertiary: var(--color-gray-400);
    --color-text-error-primary: var(--color-error-400);
    --color-text-warning-primary: var(--color-warning-400);
    --color-text-success-primary: var(--color-success-400);
    --color-text-white: var(--color-white);
    --color-text-secondary: var(--color-gray-300);
    --color-text-disabled: var(--color-gray-500);
    --color-text-secondary_hover: var(--color-gray-200);
    --color-text-tertiary_hover: var(--color-gray-300);
    --color-text-brand-secondary: var(--color-gray-300);
    --color-text-placeholder: var(--color-gray-500);
    --color-text-placeholder_subtle: var(--color-gray-700);
    --color-text-brand-tertiary: var(--color-gray-400);
    --color-text-editor-icon-fg: var(--color-gray-400);
    --color-text-editor-icon-fg_active: var(--color-white);
    --color-text-quaternary: var(--color-gray-400);
    --color-text-brand-primary: var(--color-gray-50);
    --color-text-primary_on-brand: var(--color-gray-50);
    --color-text-secondary_on-brand: var(--color-gray-300);
    --color-text-tertiary_on-brand: var(--color-gray-400);
    --color-text-quaternary_on-brand: var(--color-gray-400);
    --color-text-brand-tertiary_alt: var(--color-gray-50);
    --color-text-brand-secondary_hover: var(--color-gray-200);
    --color-text-error-primary_hover: var(--color-error-300);

    /* BORDER COLORS */
    --color-border-secondary: var(--color-gray-800);
    --color-border-error_subtle: var(--color-error-500);
    --color-border-primary: var(--color-gray-700);
    --color-border-brand: var(--color-brand-400);
    --color-border-disabled: var(--color-gray-700);
    --color-border-error: var(--color-error-400);
    --color-border-disabled_subtle: var(--color-gray-800);
    --color-border-tertiary: var(--color-gray-800);
    --color-border-brand_alt: var(--color-gray-700);
    --color-border-secondary_alt: var(--color-gray-800);

    /* FOREGROUND COLORS */
    --color-fg-secondary: var(--color-gray-300);
    --color-fg-warning-primary: var(--color-warning-500);
    --color-fg-success-primary: var(--color-success-500);
    --color-fg-white: var(--color-white);
    --color-fg-success-secondary: var(--color-success-400);
    --color-fg-secondary_hover: var(--color-gray-200);
    --color-fg-primary: var(--color-white);
    --color-fg-disabled: var(--color-gray-500);
    --color-fg-brand-secondary: var(--color-brand-500);
    --color-fg-brand-primary: var(--color-brand-500);
    --color-fg-quaternary: var(--color-gray-600);
    --color-fg-quaternary_hover: var(--color-gray-500);
    --color-fg-error-primary: var(--color-error-500);
    --color-fg-disabled_subtle: var(--color-gray-600);
    --color-fg-warning-secondary: var(--color-warning-400);
    --color-fg-error-secondary: var(--color-error-400);
    --color-fg-tertiary: var(--color-gray-400);
    --color-fg-tertiary_hover: var(--color-gray-300);
    --color-fg-brand-primary_alt: var(--color-gray-300);
    --color-fg-brand-secondary_alt: var(--color-gray-600);
    --color-fg-brand-secondary_hover: var(--color-gray-500);

    /* BACKGROUND COLORS */
    --color-bg-primary: var(--color-gray-950);
    --color-bg-tertiary: var(--color-gray-800);
    --color-bg-brand-primary: var(--color-brand-500);
    --color-bg-error-secondary: var(--color-error-600);
    --color-bg-warning-primary: var(--color-warning-950);
    --color-bg-warning-secondary: var(--color-warning-600);
    --color-bg-success-primary: var(--color-success-950);
    --color-bg-success-secondary: var(--color-success-600);
    --color-bg-brand-solid: var(--color-brand-600);
    --color-bg-secondary-solid: var(--color-gray-600);
    --color-bg-error-solid: var(--color-error-600);
    --color-bg-warning-solid: var(--color-warning-600);
    --color-bg-success-solid: var(--color-success-600);
    --color-bg-secondary_hover: var(--color-gray-800);
    --color-bg-primary_hover: var(--color-gray-800);
    --color-bg-disabled: var(--color-gray-800);
    --color-bg-active: var(--color-gray-800);
    --color-bg-brand-solid_hover: var(--color-brand-500);
    --color-bg-error-primary: var(--color-error-950);
    --color-bg-brand-secondary: var(--color-brand-600);
    --color-bg-secondary: var(--color-gray-900);
    --color-bg-disabled_subtle: var(--color-gray-900);
    --color-bg-quaternary: var(--color-gray-700);
    --color-bg-primary_alt: var(--color-bg-secondary);
    --color-bg-brand-primary_alt: var(--color-bg-secondary);
    --color-bg-secondary_alt: var(--color-bg-primary);
    --color-bg-overlay: var(--color-gray-800);
    --color-bg-secondary_subtle: var(--color-gray-900);
    --color-bg-brand-section: var(--color-bg-secondary);
    --color-bg-brand-section_subtle: var(--color-bg-primary);
    --color-bg-primary-solid: var(--color-bg-secondary);

    /* COMPONENT COLORS */
    --color-app-store-badge-border: var(--color-white);
    --color-avatar-bg: var(--color-gray-800);
    --color-avatar-contrast-border: rgb(255 255 255 / 0.12);
    --color-avatar-profile-photo-border: var(--color-gray-950);
    --color-avatar-styles-bg-neutral: rgb(224 224 224 / 1);
    --color-button-destructive-primary-icon: var(--color-error-300);
    --color-button-destructive-primary-icon_hover: var(--color-error-200);
    --color-button-primary-icon: var(--color-brand-300);
    --color-button-primary-icon_hover: var(--color-brand-200);
    --color-featured-icon-light-fg-brand: var(--color-brand-200);
    --color-featured-icon-light-fg-error: var(--color-error-200);
    --color-featured-icon-light-fg-gray: var(--color-gray-200);
    --color-featured-icon-light-fg-success: var(--color-success-200);
    --color-featured-icon-light-fg-warning: var(--color-warning-200);
    --color-focus-ring-error: var(--color-error-500);
    --color-focus-ring: var(--color-brand-500);
    --color-footer-button-fg: var(--color-gray-300);
    --color-footer-button-fg_hover: var(--color-gray-100);
    --color-icon-fg-brand: var(--color-gray-400);
    --color-icon-fg-brand_on-brand: var(--color-gray-400);
    --color-nav-item-button-icon-fg: var(--color-gray-400);
    --color-nav-item-button-icon-fg_active: var(--color-gray-300);
    --color-nav-item-icon-fg: var(--color-gray-400);
    --color-nav-item-icon-fg_active: var(--color-gray-300);
    --color-screen-mockup-border: var(--color-gray-700);
    --color-slider-handle-bg: var(--color-fg-brand-primary);
    --color-slider-handle-border: var(--color-bg-primary);
    --color-toggle-border: var(--color-transparent);
    --color-toggle-button-fg_disabled: var(--color-gray-600);
    --color-toggle-slim-border_pressed-hover: var(--color-transparent);
    --color-toggle-slim-border_pressed: var(--color-transparent);
    --color-tooltip-supporting-text: var(--color-gray-300);
    --color-text-editor-icon-fg: var(--color-gray-400);
    --color-text-editor-icon-fg_active: var(--color-white);

    /* BACKGROUND PROPERTY COLORS */
    --background-color-quaternary: var(--color-bg-quaternary);
    --background-color-brand-solid: var(--color-bg-brand-solid);
    --background-color-disabled: var(--color-bg-disabled);
    --background-color-primary: var(--color-bg-primary);
    --background-color-primary-solid: var(--color-bg-primary-solid);
    --background-color-primary_alt: var(--color-bg-primary_alt);
    --background-color-primary_hover: var(--color-bg-primary_hover);
    --background-color-secondary: var(--color-bg-secondary);
    --background-color-secondary-solid: var(--color-bg-secondary-solid);
    --background-color-secondary_alt: var(--color-bg-secondary_alt);
    --background-color-secondary_hover: var(--color-bg-secondary_hover);
    --background-color-secondary_subtle: var(--color-bg-secondary_subtle);
    --background-color-tertiary: var(--color-bg-tertiary);
    --background-color-active: var(--color-bg-active);
    --background-color-disabled_subtle: var(--color-bg-disabled_subtle);
    --background-color-overlay: var(--color-bg-overlay);
    --background-color-brand-primary: var(--color-bg-brand-primary);
    --background-color-brand-primary_alt: var(--color-bg-brand-primary_alt);
    --background-color-brand-secondary: var(--color-bg-brand-secondary);
    --background-color-brand-solid: var(--color-bg-brand-solid);
    --background-color-brand-solid_hover: var(--color-bg-brand-solid_hover);
    --background-color-brand-section: var(--color-bg-brand-section);
    --background-color-brand-section_subtle: var(
      --color-bg-brand-section_subtle
    );
    --background-color-error-primary: var(--color-bg-error-primary);
    --background-color-error-secondary: var(--color-bg-error-secondary);
    --background-color-error-solid: var(--color-bg-error-solid);
    --background-color-warning-primary: var(--color-bg-warning-primary);
    --background-color-warning-secondary: var(--color-bg-warning-secondary);
    --background-color-warning-solid: var(--color-bg-warning-solid);
    --background-color-success-primary: var(--color-bg-success-primary);
    --background-color-success-secondary: var(--color-bg-success-secondary);
    --background-color-success-solid: var(--color-bg-success-solid);
    --background-color-border-brand: var(--color-border-brand);
    --background-color-border-tertiary: var(--color-border-tertiary);
    --background-color-border-brand_alt: var(--color-border-brand_alt);

    /* TEXT PROPERTY COLORS */
    --text-color-primary: var(--color-text-primary);
    --text-color-primary_on-brand: var(--color-text-primary_on-brand);
    --text-color-secondary: var(--color-text-secondary);
    --text-color-secondary_hover: var(--color-text-secondary_hover);
    --text-color-secondary_on-brand: var(--color-text-secondary_on-brand);
    --text-color-tertiary: var(--color-text-tertiary);
    --text-color-tertiary_hover: var(--color-text-tertiary_hover);
    --text-color-tertiary_on-brand: var(--color-text-tertiary_on-brand);
    --text-color-quaternary: var(--color-text-quaternary);
    --text-color-quaternary_on-brand: var(--color-text-quaternary_on-brand);
    --text-color-disabled: var(--color-text-disabled);
    --text-color-placeholder: var(--color-text-placeholder);
    --text-color-placeholder_subtle: var(--color-text-placeholder_subtle);
    --text-color-brand-primary: var(--color-text-brand-primary);
    --text-color-brand-secondary: var(--color-text-brand-secondary);
    --text-color-brand-tertiary: var(--color-text-brand-tertiary);
    --text-color-brand-tertiary_alt: var(--color-text-brand-tertiary_alt);
    --text-color-error-primary: var(--color-text-error-primary);
    --text-color-warning-primary: var(--color-text-warning-primary);
    --text-color-success-primary: var(--color-text-success-primary);
    --text-color-tooltip-supporting-text: var(--color-tooltip-supporting-text);

    /* BORDER PROPERTY COLORS */
    --border-color-primary: var(--color-border-primary);
    --border-color-secondary: var(--color-border-secondary);
    --border-color-secondary_alt: var(--color-border-secondary_alt);
    --border-color-tertiary: var(--color-border-tertiary);
    --border-color-disabled: var(--color-border-disabled);
    --border-color-brand: var(--color-border-brand);
    --border-color-brand-solid: var(--color-bg-brand-solid);
    --border-color-brand-solid_hover: var(--color-bg-brand-solid_hover);
    --border-color-error: var(--color-border-error);
    --border-color-disabled_subtle: var(--color-border-disabled_subtle);
    --border-color-brand_alt: var(--color-border-brand_alt);
    --border-color-error_subtle: var(--color-border-error_subtle);

    /* RING PROPERTY COLORS */
    --ring-color-bg-brand-solid: var(--color-bg-brand-solid);
    --ring-color-primary: var(--color-border-primary);
    --ring-color-secondary: var(--color-border-secondary);
    --ring-color-secondary_alt: var(--color-border-secondary_alt);
    --ring-color-tertiary: var(--color-border-tertiary);
    --ring-color-disabled: var(--color-border-disabled);
    --ring-color-brand: var(--color-border-brand);
    --ring-color-brand-solid: var(--color-bg-brand-solid);
    --ring-color-brand-solid_hover: var(--color-bg-brand-solid_hover);
    --ring-color-error: var(--color-border-error);
    --ring-color-disabled_subtle: var(--color-border-disabled_subtle);
    --ring-color-brand_alt: var(--color-border-brand_alt);
    --ring-color-error_subtle: var(--color-border-error_subtle);

    /* OUTLINE PROPERTY COLORS */
    --outline-color-brand: var(--color-border-brand);
    --outline-color-primary: var(--color-border-primary);
    --outline-color-secondary: var(--color-border-secondary);
    --outline-color-secondary_alt: var(--color-border-secondary_alt);
    --outline-color-tertiary: var(--color-border-tertiary);
    --outline-color-disabled: var(--color-border-disabled);
    --outline-color-brand: var(--color-border-brand);
    --outline-color-brand-solid: var(--color-bg-brand-solid);
    --outline-color-brand-solid_hover: var(--color-bg-brand-solid_hover);
    --outline-color-error: var(--color-border-error);
    --outline-color-disabled_subtle: var(--color-border-disabled_subtle);
    --outline-color-brand_alt: var(--color-border-brand_alt);
    --outline-color-error_subtle: var(--color-border-error_subtle);
  }
}
