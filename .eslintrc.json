{
  "extends": [
    "next/core-web-vitals",
    "eslint-config-prettier",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended"
  ],
  "plugins": ["prettier"],
  "rules": {
    "@typescript-eslint/no-explicit-any": "off", //允许使用any
    "@typescript-eslint/ban-ts-comment": "off", //允许使用@ts-ignore
    "@typescript-eslint/no-non-null-assertion": "off", //允许使用非空断言
    "@typescript-eslint/no-unused-vars": [
      "warn",
      { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }
    ],
    
    "@typescript-eslint/no-var-requires": "off" //允许使用CommonJS的写
  }
}
